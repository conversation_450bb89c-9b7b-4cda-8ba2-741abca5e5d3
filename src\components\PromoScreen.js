import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView } from 'react-native';
import { Checkbox, Button, Surface, Title, Paragraph, useTheme } from 'react-native-paper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import FixedFontButton from './FixedFontButton';
import AdmobService from '../services/AdmobService';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import StickyBottomAdMob from '../components/StickyBottomAdMob';
const PromoScreen = () => {
    const { colors } = useTheme();
    const [dontShowAgain, setDontShowAgain] = useState(false);
    const [isDismissed, setIsDismissed] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [shouldShow, setShouldShow] = useState(true);
    const navigation = useNavigation();

    // Check if we should show this screen at all
    useEffect(() => {
        let isMounted = true;

        const checkPromoDismissal = async () => {
            try {
                const promoDismissed = await AsyncStorage.getItem('promoDismissed');

                if (!isMounted) return;

                if (promoDismissed && new Date(promoDismissed) > new Date()) {
                    // If promo was dismissed and expiry is in the future, navigate away
                    setShouldShow(false);
                    navigation.replace('MainTabs');
                } else {
                    setShouldShow(true);
                }
            } catch (error) {
                console.error('Error checking promo dismissal:', error);
            } finally {
                if (isMounted) {
                    setIsLoading(false);
                }
            }
        };

        checkPromoDismissal();

        return () => {
            isMounted = false;
        };
    }, [navigation]);

    const handleDismiss = async () => {
        // Prevent multiple clicks
        if (isDismissed) return;
        setIsDismissed(true);

        try {
            if (dontShowAgain) {
                const today = new Date();
                const expiry = new Date(today);
                expiry.setHours(23, 59, 59, 999); // Set expiry to end of the day
                await AsyncStorage.setItem('promoDismissed', expiry.toString());
            }

            // Use replace instead of navigate to prevent going back to this screen
            navigation.replace('MainTabs');
        } catch (error) {
            console.error('Error in handleDismiss:', error);
            setIsDismissed(false); // Reset if there's an error
        }
    };

    // Don't render anything while checking AsyncStorage
    if (isLoading) {
        return null;
    }

    // Don't render if we shouldn't show this screen
    if (!shouldShow) {
        return null;
    }

    return (
        <SafeAreaView style={styles.safeArea}>
            <ScrollView
                contentContainerStyle={{
                    alignItems: "center",
                    justifyContent: "center",
                    padding: 16, // Move padding here if it affects the content layout
                }}
                keyboardShouldPersistTaps="handled"
            >
                <Surface style={styles.promoCard}>
                    <Title maxFontSizeMultiplier={1.2} style={styles.title}>Unlock Your AWS Certification Success</Title>

                    <View style={styles.highlightBadge}>
                        <Text maxFontSizeMultiplier={1.2} style={styles.badgeText}>LIMITED TIME OFFER</Text>
                    </View>

                    {/* <Paragraph maxFontSizeMultiplier={1.2}  style={styles.subtitle}>
                    Complete access to our comprehensive AWS exam preparation platform
                </Paragraph> */}

                    {/* <View style={styles.divider} /> */}

                    <View style={styles.featureItem}>
                        <Text maxFontSizeMultiplier={1.2} style={[styles.bulletPoint, { color: colors.onSurface }]}>•</Text>
                        <Paragraph maxFontSizeMultiplier={1.2} style={[styles.featureText, { color: colors.onSurface }]}>
                            <Text style={[styles.bold, { color: colors.onSurface }]}>
                                5,000+ real verified past exam questions
                            </Text> covering all AWS certifications. No fake exam questions!
                        </Paragraph>
                    </View>
                    <View style={styles.featureItem}>
                        <Text maxFontSizeMultiplier={1.2} style={[styles.bulletPoint, { color: colors.onSurface }]}>•</Text>
                        <Paragraph maxFontSizeMultiplier={1.2} style={[styles.featureText, { color: colors.onSurface }]}>
                            <Text style={[styles.bold, { color: colors.onSurface }]}>
                                Detailed explanations
                            </Text> for every answer to enhance understanding
                        </Paragraph>
                    </View>
                    <View style={styles.featureItem}>
                        <Text maxFontSizeMultiplier={1.2} style={[styles.bulletPoint, { color: colors.onSurface }]}>•</Text>
                        <Paragraph maxFontSizeMultiplier={1.2} style={[styles.featureText, { color: colors.onSurface }]}>
                            <Text style={[styles.bold, { color: colors.onSurface }]}>
                                Personalized progress tracking
                            </Text> to identify your strengths and weaknesses
                        </Paragraph>
                    </View>
                    <View style={styles.featureItem}>
                        <Text maxFontSizeMultiplier={1.2} style={[styles.bulletPoint, { color: colors.onSurface }]}>•</Text>
                        <Paragraph maxFontSizeMultiplier={1.2} style={[styles.featureText, { color: colors.onSurface }]}>
                            <Text style={[styles.bold, { color: colors.onSurface }]}>
                                AI-powered assistance
                            </Text> to help clarify complex concepts
                        </Paragraph>
                    </View>
                    <View style={styles.pricingSection}>
                        <View style={styles.pricingRow}>
                            <Text maxFontSizeMultiplier={1.2} style={[styles.regularPrice, { color: colors.onSurface }]}>
                                $15/month
                            </Text>
                            <Text maxFontSizeMultiplier={1.2} style={[styles.specialPrice]}>
                                FREE
                            </Text>
                        </View>
                        <Paragraph maxFontSizeMultiplier={1.2} style={[styles.offerDetails, { color: colors.onSurface }]}>
                            Limited-time offer: Start your AWS exam prep now!
                        </Paragraph>
                    </View>
                    <View style={{
                        backgroundColor: colors.surface,
                    }}>
                        <View style={{
                        }}>
                            {AdmobService.renderBannerAdContainer(BannerAdSize.BANNER, (error) => {
                                console.log('Banner ad failed to load:', error);
                                setAdError(error.message);
                            })}
                        </View>
                    </View>
                    <Paragraph maxFontSizeMultiplier={1.2} style={[styles.offerDetails, { marginBottom: 0 }]}>
                        This app is ad-supported.
                    </Paragraph>
                </Surface>

                <View style={styles.actionSection}>
                    <View style={styles.checkboxContainer}>
                        <Checkbox
                            status={dontShowAgain ? 'checked' : 'unchecked'}
                            onPress={() => setDontShowAgain(!dontShowAgain)}
                            disabled={isDismissed}
                        />
                        <Text maxFontSizeMultiplier={1.2} style={[styles.label,{color: colors.onSurface}]}>Don't show this again today</Text>
                    </View>

                    <FixedFontButton
                        mode="contained"
                        onPress={handleDismiss}
                        style={styles.button}
                        labelStyle={{ fontSize: 14, color: '#FFFFFF' }}
                        disabled={isDismissed}
                        loading={isDismissed}
                    >
                        Get Started Now
                    </FixedFontButton>
                </View>
            </ScrollView>
        </SafeAreaView >
    );
};

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        marginTop: 16
    },
    promoCard: {
        width: '100%',
        paddingVertical: 10,
        paddingHorizontal: 24,
        borderRadius: 12,
        elevation: 4,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 8,
        color: '#0066cc',
    },
    highlightBadge: {
        backgroundColor: '#ff9500',
        paddingVertical: 4,
        paddingHorizontal: 12,
        borderRadius: 16,
        alignSelf: 'center',
        marginBottom: 16,
    },
    badgeText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 12,
    },
    subtitle: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 16,
        color: '#333',
    },
    divider: {
        height: 1,
        backgroundColor: '#e0e0e0',
        marginVertical: 16,
    },
    featureSection: {
    },
    featureTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
        color: '#333',
    },
    featureItem: {
        flexDirection: 'row',
        marginBottom: 10,
        alignItems: 'flex-start',
    },
    bulletPoint: {
        fontSize: 18,
        marginRight: 8,
        color: '#0066cc',
        lineHeight: 24,
    },
    featureText: {
        flex: 1,
        fontSize: 16,
        lineHeight: 22,
        color: '#444',
    },
    bold: {
        fontWeight: 'bold',
        color: '#333',
    },
    pricingRow: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 8,
    },
    regularPrice: {
        fontSize: 18,
        color: '#888',
        textDecorationLine: 'line-through',
        marginRight: 12,
    },
    specialPrice: {
        fontSize: 24,
        fontWeight: 'bold',
        color: 'red',
    },
    offerDetails: {
        fontSize: 14,
        textAlign: 'center',
        fontStyle: 'italic',
        color: '#666',
        marginBottom: 20,
    },
    actionSection: {
        width: '100%',
        marginTop: 16,
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    label: {
        marginLeft: 8,
        fontSize: 14,
        color: '#666',
    },
    button: {
        borderRadius: 8,
        paddingVertical: 6,
    },
    buttonLabel: {
        fontSize: 16,
        fontWeight: 'bold',
        paddingVertical: 2,
    },
});

export default PromoScreen;