module.exports = function (api) {
  api.cache(false);
  
  // Determine environment file based on APP_ENV and APP_VARIANT
  let envFile = '.env';

  if (process.env.APP_ENV === 'dev' && process.env.APP_VARIANT === 'pmi') {
    envFile = '.env.pmi.dev';
  } else if (process.env.APP_ENV === 'dev') {
    envFile = '.env.dev';
  } else if (process.env.APP_VARIANT === 'pmi') {
    envFile = '.env.pmi';
  } else if (process.env.APP_VARIANT === 'aws') {
    envFile = '.env';
  }
  
  return {
    presets: ['module:@react-native/babel-preset'],
    plugins: [
      [
        'module:react-native-dotenv',
        {
          path: envFile,
          allowUndefined: false,
          safe: false,
          allowlist: null,
          blocklist: null,
        }
      ],
      'react-native-reanimated/plugin'
    ],
  };
};