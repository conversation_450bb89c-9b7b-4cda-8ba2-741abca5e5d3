# Development Environment Configuration
APP_NAME=5000+ AWS Dump (Dev)
APP_DISPLAY_NAME=5000+ AWS Dump (Dev)
APP_ID=com.prepfy.qna.dev
EXAM_TYPE=AWS

WP_API_URL=https://hkit.supply/wp-json/base/v1/login_with_google
NODE_API_URL=http://localhost:3016/api
API_TOKEN=test-token
CACHE_TTL=1800000  # 30 minutes

# RevenueCat Configuration - Development
REVENUECAT_API_KEY=goog_GmdIeGpDtpWugBpnXrkffAobfIL
REVENUECAT_REST_API_KEY=sk_dCgEfuwtFLrtbRWIYjtoZgHodnwwN
REVENUECAT_PROJECT_ID=projea5e491f

# AdMob Configuration - Development (Test App ID)
ADMOB_APP_ID=ca-app-pub-3940256099942544~3347511713

# Credit Configuration
CREDIT_VIA_AD=20
CREDIT_VIA_PURCHASE=100

# Real-time sync configuration
ENABLE_REALTIME_SYNC=true      # Enable for development testing
USER_PROGRESS_SYNC_WINDOW=5   # Faster sync for development
AI_CHAT_SYNC_WINDOW=5         # Faster sync for development

# AI Credit Configuration
REVENUECAT_AI_CREDIT_WEEKLY=50
REVENUECAT_AI_CREDIT_MONTHLY=300
REVENUECAT_AI_CREDIT_BIMONTHLY=1000

IS_UNLOCKED=true