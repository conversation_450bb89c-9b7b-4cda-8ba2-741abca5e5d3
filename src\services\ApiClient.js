import { Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_TOKEN, NODE_API_URL, APP_ENV } from '@env'; // Add APP_ENV import
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { VENDOR_CODE } from '@env';

// Debug logger function
const debug = (message, ...args) => {
    console.log(`[apiclient] ${message}`, ...args);
};

// Log environment variables for debugging
console.log('[ApiClient] Environment variables:', {
    NODE_API_URL: NODE_API_URL || 'not set',
    APP_ENV: APP_ENV || 'not set',
    API_TOKEN_LENGTH: API_TOKEN ? API_TOKEN.length : 0
});

class ApiClient {
    constructor() {
        this.baseUrl = NODE_API_URL;
        this.token = API_TOKEN;
        this.cache = new Map();
        this.cacheTTL = 30 * 60 * 1000; // 30 minutes
        this.cacheEnabled = APP_ENV !== 'dev';
        this.defaultTimeout = 15000; // 15 seconds
        this.retryCount = 1; // No retries - keep it simple
        this.retryDelay = 1000;
        this.cacheCleanupInterval = 5 * 60 * 1000; // Clean cache every 5 minutes

        // Log ApiClient configuration
        console.log('[ApiClient] Initialized with:', {
            baseUrl: this.baseUrl || 'not set',
            tokenLength: this.token ? this.token.length : 0,
            cacheEnabled: this.cacheEnabled,
            environment: APP_ENV
        });
        this.cleanupIntervalId = null;
        this.abortedCleanupIntervalId = null;

        // Try to load the auth token from AsyncStorage
        this.initializeAuthToken();

        // Custom headers for requests
        this.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };

        // Map to track in-flight requests
        this.pendingRequests = new Map();
        // Map to track request metadata (signal, controller, etc.) for deduplication logic
        this.pendingRequestsInfo = new Map();
        // Map to track failed requests for independent retry
        this.failedRequests = new Map();
        // Enable request deduplication by default
        this.deduplicationEnabled = true;

        // Start cache cleanup interval
        if (this.cacheEnabled) {
            this.startCacheCleanup();
        }

        // Start periodic cleanup of aborted requests
        this.startAbortedRequestCleanup();

        debug('Initializing ApiClient with configuration:', {
            baseUrl: this.baseUrl,
            tokenLength: this.token ? this.token.length : 0,
            cacheEnabled: this.cacheEnabled,
            defaultTimeout: this.defaultTimeout,
            retryCount: this.retryCount,
            retryDelay: this.retryDelay,
            deduplicationEnabled: this.deduplicationEnabled,
            environment: APP_ENV,
            platform: Platform.OS
        });

        if (!this.baseUrl) {
            throw new Error('NODE_API_URL is not defined');
        }

        if (typeof this.baseUrl !== 'string') {
            throw new Error('NODE_API_URL must be a string');
        }

        if (Platform.OS === 'android') {
            this.baseUrl = this.baseUrl
                .replace('localhost', '********')
                .replace('127.0.0.1', '********');
            debug('Android platform detected, adjusted baseUrl:', this.baseUrl);
        }
    }

    /**
     * Set a custom header for all requests
     * @param {string} key - Header key
     * @param {string} value - Header value
     */
    setHeader(key, value) {
        this.headers[key] = value;
        debug(`Set header: ${key}`);
    }

    /**
     * Remove a custom header
     * @param {string} key - Header key to remove
     */
    removeHeader(key) {
        delete this.headers[key];
        debug(`Removed header: ${key}`);
    }

    /**
     * Get all current headers
     * @returns {Object} - Current headers
     */
    getHeaders() {
        return { ...this.headers };
    }

    /**
     * Initialize authentication token from AsyncStorage
     * This is called during construction
     */
    async initializeAuthToken() {
        try {
            const storedToken = await AsyncStorage.getItem('auth_token');
            if (storedToken && typeof storedToken === 'string' && storedToken.length > 10) {
                this.token = storedToken;
                console.log('[ApiClient] Loaded auth token from AsyncStorage, length:', storedToken.length);
                debug('Loaded auth token from AsyncStorage');
            } else {
                console.log('[ApiClient] No valid auth token found in AsyncStorage, using default API token');
                debug('Using default API token (no valid token in AsyncStorage)');
            }
        } catch (error) {
            console.error('[ApiClient] Error loading auth token from AsyncStorage:', error);
            debug('Error loading auth token from AsyncStorage');
        }
    }

    /**
     * Set authentication token for API requests
     * @param {string} token - JWT authentication token
     */
    setAuthToken(token) {
        // Check if token is valid
        if (token && typeof token === 'string' && token.length > 10) {
            this.token = token;
            console.log('[ApiClient] Auth token set successfully, length:', token.length);
            console.log('[ApiClient] token:', token);
            debug('Auth token set successfully');
        } else {
            // If token is invalid, use default API token
            this.token = API_TOKEN;
            console.log('[ApiClient] Invalid token provided, using default API token');
            debug('Using default API token due to invalid token');
        }
    }

    /**
     * Clear authentication token and reset to default API token
     */
    clearAuthToken() {
        console.log('[ApiClient] Clearing auth token, resetting to default API token');
        this.token = API_TOKEN;
        debug('Auth token cleared, using default API token');
    }

    /**
     * Check if current token is valid (not the default API token)
     * @returns {boolean} - True if we have a custom auth token
     */
    hasValidAuthToken() {
        const isValid = this.token && this.token !== API_TOKEN && this.token.length > 10;
        debug(`Auth token validation: ${isValid ? 'valid' : 'invalid'}`, {
            hasToken: !!this.token,
            isDefaultToken: this.token === API_TOKEN,
            tokenLength: this.token ? this.token.length : 0
        });
        return isValid;
    }

    /**
     * Get current authentication status
     * @returns {Object} - Authentication status details
     */
    getAuthStatus() {
        return {
            hasToken: !!this.token,
            isDefaultToken: this.token === API_TOKEN,
            tokenLength: this.token ? this.token.length : 0,
            isValid: this.hasValidAuthToken()
        };
    }

    /**
     * Generate a unique request key based on endpoint and options
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {string} - Unique request key
     */
    generateRequestKey(endpoint, options = {}) {
        // Create a stable string representation of options by sorting keys
        const stableStringify = (obj) => JSON.stringify(Object.keys(obj).sort().reduce((acc, key) => {
            // Skip signal and onTimeout as they're unique per request
            if (key !== 'signal' && key !== 'onTimeout') {
                acc[key] = obj[key];
            }
            return acc;
        }, {}));

        return `${endpoint}-${stableStringify(options)}`;
    }

    /**
     * Start the cache cleanup interval
     */
    startCacheCleanup() {
        // Clear any existing interval
        if (this.cleanupIntervalId) {
            clearInterval(this.cleanupIntervalId);
        }

        // Set up new interval
        this.cleanupIntervalId = setInterval(() => {
            this.cleanExpiredCache();
        }, this.cacheCleanupInterval);

        console.log('[ApiClient] Cache cleanup interval started');
    }

    /**
     * Stop the cache cleanup interval
     */
    stopCacheCleanup() {
        if (this.cleanupIntervalId) {
            clearInterval(this.cleanupIntervalId);
            this.cleanupIntervalId = null;
            console.log('[ApiClient] Cache cleanup interval stopped');
        }
    }

    /**
     * Start the aborted request cleanup interval
     */
    startAbortedRequestCleanup() {
        // Clear any existing interval
        if (this.abortedCleanupIntervalId) {
            clearInterval(this.abortedCleanupIntervalId);
        }

        // Set up new interval - clean up every 30 seconds
        this.abortedCleanupIntervalId = setInterval(() => {
            this.cleanupAbortedRequests();
        }, 30000);

        console.log('[ApiClient] Aborted request cleanup interval started');
    }

    /**
     * Stop the aborted request cleanup interval
     */
    stopAbortedRequestCleanup() {
        if (this.abortedCleanupIntervalId) {
            clearInterval(this.abortedCleanupIntervalId);
            this.abortedCleanupIntervalId = null;
            console.log('[ApiClient] Aborted request cleanup interval stopped');
        }
    }

    /**
     * Clean expired cache entries
     */
    cleanExpiredCache() {
        const now = Date.now();
        let expiredCount = 0;

        Array.from(this.cache.entries()).forEach(([key, { timestamp }]) => {
            if (now - timestamp >= this.cacheTTL) {
                this.cache.delete(key);
                expiredCount++;
            }
        });

        if (expiredCount > 0) {
            console.log(`[ApiClient] Cleaned up ${expiredCount} expired cache entries`);
        }
    }

    /**
     * Show network error alert to user
     * @param {string} endpoint - The endpoint that failed
     * @param {Error} error - The error that occurred
     * @param {Object} options - Additional options
     */
    showNetworkErrorAlert(endpoint, error, options = {}) {
        // Only show alerts for actual timeout errors, not external aborts
        if (error.name === 'AbortError' && !options.wasAbortedDueToTimeout) {
            debug(`Skipping alert for externally aborted request: ${endpoint}`);
            return;
        }

        const isTimeout = options.wasAbortedDueToTimeout || error.message.includes('timeout');
        const title = isTimeout ? 'Request Timeout' : 'Network Error';

        let message;
        if (isTimeout) {
            message = 'The request took too long to complete. Please try again later.';
        } else {
            message = 'There was a problem connecting to the server. Please check your internet connection and try again later.';
        }

        debug(`Showing ${isTimeout ? 'timeout' : 'network'} error alert for ${endpoint}`);

        // Simple rate limiting - don't show more than one alert per 3 seconds
        if (!this._isShowingTooManyAlerts()) {
            Alert.alert(title, message, [{ text: 'OK' }]);
        } else {
            debug('Suppressing additional error alert to prevent spam');
        }
    }

    /**
     * Check if we're showing too many alerts
     * @returns {boolean} - True if we should suppress additional alerts
     * @private
     */
    _isShowingTooManyAlerts() {
        if (!ApiClient._lastAlertTime) {
            ApiClient._lastAlertTime = 0;
        }

        const now = Date.now();
        const timeSinceLastAlert = now - ApiClient._lastAlertTime;

        // Only allow one alert per 3 seconds
        if (timeSinceLastAlert < 3000) {
            return true;
        }

        ApiClient._lastAlertTime = now;
        return false;
    }

    /**
     * Retry a specific failed request independently
     * @param {string} requestKey - The key of the failed request
     * @returns {Promise} - The result of the retried request
     */
    async retryFailedRequest(requestKey) {
        if (!this.failedRequests.has(requestKey)) {
            throw new Error(`No failed request found with key: ${requestKey}`);
        }

        const { endpoint, options } = this.failedRequests.get(requestKey);
        console.log(`[ApiClient] Retrying failed request: ${endpoint}`);

        // Remove from failed requests map
        this.failedRequests.delete(requestKey);

        // Make a fresh request
        return this.request(endpoint, options);
    }

    /**
     * Validate and refresh Google token if needed
     * @returns {Promise<boolean>} - True if token is valid or was successfully refreshed
     */
    async validateAndRefreshGoogleToken() {
        try {
            // Only validate if we have a custom auth token (not the default API token)
            if (!this.hasValidAuthToken()) {
                debug('No custom auth token to validate');
                return true; // Default token is always "valid" for our purposes
            }

            console.log('[ApiClient] Validating Google token...');

            // Check if user is currently signed in with Google
            const currentUser = await GoogleSignin.getCurrentUser();

            if (!currentUser) {
                console.log('[ApiClient] No current Google user found, token may be expired');
                return false;
            }

            // If we have a current user, the token should be valid
            console.log('[ApiClient] Google token validation successful');
            return true;

        } catch (error) {
            console.log('[ApiClient] Google token validation failed:', error.message);

            // Try to refresh the token silently
            try {
                console.log('[ApiClient] Attempting silent token refresh...');
                const refreshedUser = await GoogleSignin.signInSilently();

                if (refreshedUser && refreshedUser.idToken) {
                    console.log('[ApiClient] Token refreshed successfully, updating auth token');
                    this.setAuthToken(refreshedUser.idToken);

                    // Store the refreshed token
                    await AsyncStorage.setItem('google_id_token', refreshedUser.idToken);

                    return true;
                } else {
                    console.log('[ApiClient] Silent refresh succeeded but no token received');
                    return false;
                }
            } catch (refreshError) {
                console.log('[ApiClient] Silent token refresh failed:', refreshError.message);
                return false;
            }
        }
    }

    async request(endpoint, options = {}) {
        const requestId = Math.random().toString(36).substring(2, 8);

        // Generate a unique key for this request for both caching and deduplication
        const requestKey = this.generateRequestKey(endpoint, options);

        debug(`[${requestId}] Request started: ${endpoint}`, {
            method: options.method || 'GET',
            hasBody: !!options.body,
            timeout: options.timeout || this.defaultTimeout,
            requestKey
        });

        // Check cache first if enabled
        if (this.cacheEnabled && this.cache.has(requestKey)) {
            const { data, timestamp } = this.cache.get(requestKey);
            const age = Date.now() - timestamp;
            if (age < this.cacheTTL) {
                debug(`[${requestId}] Cache hit for: ${endpoint} (age: ${Math.round(age / 1000)}s)`);
                return data;
            } else {
                this.cache.delete(requestKey);
                debug(`[${requestId}] Removed expired cache for: ${endpoint}`);
            }
        }

        // Check for duplicate in-flight requests if deduplication is enabled
        if (this.deduplicationEnabled && this.pendingRequests.has(requestKey)) {
            const existingPromise = this.pendingRequests.get(requestKey);

            // Check if the existing promise is associated with an aborted signal
            // If so, remove it from pending requests and proceed with a new request
            const existingRequestInfo = this.pendingRequestsInfo?.get(requestKey);
            if (existingRequestInfo && existingRequestInfo.signal && existingRequestInfo.signal.aborted) {
                debug(`[${requestId}] Existing request was aborted, removing from deduplication: ${endpoint}`);
                this.pendingRequests.delete(requestKey);
                this.pendingRequestsInfo?.delete(requestKey);
            } else {
                debug(`[${requestId}] Deduplicating request: ${endpoint}`);
                debug(`[${requestId}] Current pending requests: ${this.pendingRequests.size}`);
                debug(`[${requestId}] Pending request keys:`, Array.from(this.pendingRequests.keys()));
                return existingPromise;
            }
        }

        // Use provided signal or create new AbortController
        const controller = options.signal ? null : new AbortController();
        const signal = options.signal || controller.signal;
        const timeout = options.timeout || this.defaultTimeout;

        debug(`[${requestId}] Using timeout: ${timeout}ms for ${endpoint}`, {
            hasExternalSignal: !!options.signal,
            isAIRequest: endpoint.includes('/ai/'),
            timeout
        });

        // Set up timeout only if we created our own controller
        let timeoutId = null;
        if (controller) {
            timeoutId = setTimeout(() => {
                controller.abort();
                debug(`[${requestId}] Request timed out after ${timeout}ms: ${endpoint}`);
            }, timeout);
        } else {
            debug(`[${requestId}] Using external signal - no internal timeout set`);
        }

        // Prepare headers
        const authToken = this.token || API_TOKEN;
        const headers = {
            'Authorization': `Bearer ${authToken}`,
            'X-Platform': Platform.OS,
            'X-App-Version': process.env.APP_VERSION || '1.0.0',
            'X-Request-ID': requestId,
            ...this.headers,
            ...options.headers,
        };

        debug(`[${requestId}] Request headers:`, headers);

        // Create the request promise
        const requestPromise = (async () => {
            try {
                debug(`[${requestId}] Making request to: ${this.baseUrl}${endpoint}`);
                debug(`[${requestId}] Request lifecycle: FETCH_START`);

                const startTime = Date.now();
                const response = await fetch(`${this.baseUrl}${endpoint}`, {
                    ...options,
                    headers,
                    signal,
                });
                const requestTime = Date.now() - startTime;

                debug(`[${requestId}] Request lifecycle: RESPONSE_RECEIVED`);
                debug(`[${requestId}] Response received in ${requestTime}ms for ${endpoint}`, {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok,
                    requestTime
                });

                // Clear timeout if we set one
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }

                if (!response.ok) {
                    const errorBody = await response.text();
                    debug(`[${requestId}] Request lifecycle: HTTP_ERROR`);
                    debug(`[${requestId}] HTTP error response: ${response.status} - ${response.statusText}`, {
                        status: response.status,
                        statusText: response.statusText,
                        errorBody: errorBody.substring(0, 200) + (errorBody.length > 200 ? '...' : '')
                    });

                    const error = new Error(`API Error: ${response.status} - ${response.statusText}`);
                    error.status = response.status;
                    error.statusText = response.statusText;
                    error.endpoint = endpoint;
                    error.errorBody = errorBody;
                    throw error;
                }

                let data;
                try {
                    data = await response.json();
                    if (data === undefined || data === null) {
                        throw new Error(`API returned ${data === undefined ? 'undefined' : 'null'} response`);
                    }
                } catch (jsonError) {
                    console.error(`[ApiClient] [${requestId}] Failed to parse JSON response:`, jsonError.message);
                    throw new Error(`Failed to parse JSON response: ${jsonError.message}`);
                }

                debug(`[${requestId}] Request lifecycle: JSON_PARSED`);
                debug(`[${requestId}] Response parsed successfully for ${endpoint}`);

                if (!data.success) {
                    debug(`[${requestId}] Request lifecycle: API_ERROR`);
                    debug(`[${requestId}] API reported error: ${data.message}`, {
                        success: data.success,
                        message: data.message,
                        code: data.code,
                        error: data.error
                    });
                    const error = new Error(`API Error: ${data.message}`);
                    error.status = data.code || response.status;
                    error.statusText = data.error || response.statusText;
                    error.endpoint = endpoint;
                    error.errorBody = JSON.stringify(data);
                    error.apiError = data;
                    throw error;
                }

                // Return appropriate data
                let responseData;
                if (endpoint.includes('/auth')) {
                    responseData = data; // Return full response for auth endpoints
                } else {
                    responseData = data.data !== undefined ? data.data : data;
                }

                // Update cache only if enabled
                if (this.cacheEnabled) {
                    this.cache.set(requestKey, {
                        data: responseData,
                        timestamp: Date.now()
                    });
                    debug(`[${requestId}] Request lifecycle: CACHE_UPDATED`);
                    debug(`[${requestId}] Updated cache for ${endpoint}`);
                }

                debug(`[${requestId}] Request lifecycle: SUCCESS`);
                return responseData;

            } catch (error) {
                // Clear timeout if we set one
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }

                // Determine if this was our timeout or external abort
                const wasOurTimeout = controller && error.name === 'AbortError';
                const wasExternalAbort = !controller && error.name === 'AbortError';

                if (wasExternalAbort) {
                    debug(`[${requestId}] Request lifecycle: EXTERNAL_ABORT`);
                    debug(`[${requestId}] Request was aborted by external source (not our timeout): ${endpoint}`, {
                        errorName: error.name,
                        hasController: !!controller,
                        signalAborted: signal?.aborted
                    });
                } else if (wasOurTimeout) {
                    debug(`[${requestId}] Request lifecycle: TIMEOUT`);
                    debug(`[${requestId}] Request timed out after ${timeout}ms: ${endpoint}`, {
                        timeout,
                        errorName: error.name
                    });
                } else {
                    debug(`[${requestId}] Request lifecycle: ERROR`);
                    debug(`[${requestId}] Request error: ${error.name} - ${error.message}`, {
                        errorName: error.name,
                        errorMessage: error.message,
                        endpoint,
                        hasController: !!controller,
                        signalAborted: signal?.aborted
                    });
                }

                // Store failed request for potential retry
                this.failedRequests.set(requestKey, { endpoint, options });
                debug(`[${requestId}] Added to failedRequests for potential manual retry`);

                // Show error alert only for our timeout errors and if not explicitly disabled
                if (wasOurTimeout && !options.skipErrorAlert) {
                    this.showNetworkErrorAlert(endpoint, error, {
                        wasAbortedDueToTimeout: true,
                        timeout
                    });
                }

                throw error;
            }
        })();

        // Create a wrapper promise that cleans up the pending request map when done
        const wrappedPromise = requestPromise
            .then(result => {
                this.pendingRequests.delete(requestKey);
                this.pendingRequestsInfo.delete(requestKey);
                debug(`[${requestId}] Request completed successfully: ${endpoint}`);
                return result;
            })
            .catch(error => {
                this.pendingRequests.delete(requestKey);
                this.pendingRequestsInfo.delete(requestKey);
                debug(`[${requestId}] Request failed with error: ${error.message}`);
                throw error;
            });

        // Store the wrapped promise in the pending requests map
        if (this.deduplicationEnabled) {
            this.pendingRequests.set(requestKey, wrappedPromise);
            // Store request metadata for abort detection
            this.pendingRequestsInfo.set(requestKey, {
                signal,
                controller,
                endpoint,
                requestId,
                startTime: Date.now()
            });
            debug(`[${requestId}] Added to pendingRequests map (size: ${this.pendingRequests.size})`);
        }

        return wrappedPromise;
    }

    // Cache and request deduplication management

    /**
     * Clear all cache entries
     */
    clearAllCache() {
        this.cache.clear();
        console.log('[ApiClient] Cleared all cache entries');
    }

    /**
     * Clear all pending requests
     */
    clearAllPendingRequests() {
        this.pendingRequests.clear();
        this.pendingRequestsInfo.clear();
        console.log('[ApiClient] Cleared all pending requests');
    }

    /**
     * Clear all failed requests
     */
    clearAllFailedRequests() {
        this.failedRequests.clear();
        console.log('[ApiClient] Cleared all failed requests');
    }

    /**
     * Clear everything - cache, pending requests, and failed requests
     */
    clearAll() {
        this.clearAllCache();
        this.clearAllPendingRequests();
        this.clearAllFailedRequests();
        console.log('[ApiClient] Cleared all data');
    }

    /**
     * Clean up aborted requests from deduplication maps
     * This helps prevent issues where aborted requests block new requests
     */
    cleanupAbortedRequests() {
        let cleanedCount = 0;

        Array.from(this.pendingRequestsInfo.entries()).forEach(([key, info]) => {
            if (info.signal && info.signal.aborted) {
                this.pendingRequests.delete(key);
                this.pendingRequestsInfo.delete(key);
                cleanedCount++;
                debug(`Cleaned up aborted request: ${key}`);
            }
        });

        if (cleanedCount > 0) {
            console.log(`[ApiClient] Cleaned up ${cleanedCount} aborted requests from deduplication maps`);
        }

        return cleanedCount;
    }

    /**
     * Clear cache for a specific endpoint
     * @param {string} endpoint - The endpoint to clear cache for
     */
    clearCacheForEndpoint(endpoint) {
        if (!endpoint) return;

        let count = 0;
        Array.from(this.cache.keys())
            .filter(key => key.startsWith(endpoint))
            .forEach(key => {
                this.cache.delete(key);
                count++;
            });

        console.log(`[ApiClient] Cleared ${count} cache entries for endpoint: ${endpoint}`);
    }

    /**
     * Clear pending requests for a specific endpoint
     * @param {string} endpoint - The endpoint to clear pending requests for
     */
    clearPendingRequestsForEndpoint(endpoint) {
        if (!endpoint) return;

        let count = 0;
        Array.from(this.pendingRequests.keys())
            .filter(key => key.startsWith(endpoint))
            .forEach(key => {
                this.pendingRequests.delete(key);
                this.pendingRequestsInfo.delete(key);
                count++;
            });

        console.log(`[ApiClient] Cleared ${count} pending requests for endpoint: ${endpoint}`);
    }

    /**
     * Clear failed requests for a specific endpoint
     * @param {string} endpoint - The endpoint to clear failed requests for
     */
    clearFailedRequestsForEndpoint(endpoint) {
        if (!endpoint) return;

        let count = 0;
        Array.from(this.failedRequests.keys())
            .filter(key => key.startsWith(endpoint))
            .forEach(key => {
                this.failedRequests.delete(key);
                count++;
            });

        console.log(`[ApiClient] Cleared ${count} failed requests for endpoint: ${endpoint}`);
    }

    /**
     * Clear cache and requests for a specific endpoint
     * @param {string} endpoint - The endpoint to clear
     */
    clearForEndpoint(endpoint) {
        if (!endpoint) return;

        this.clearCacheForEndpoint(endpoint);
        this.clearPendingRequestsForEndpoint(endpoint);
        this.clearFailedRequestsForEndpoint(endpoint);
        console.log(`[ApiClient] Cleared all data for endpoint: ${endpoint}`);
    }

    /**
     * Clear cache by specific request key
     * @param {string} requestKey - The exact request key to clear
     */
    clearCacheByKey(requestKey) {
        if (this.cache.has(requestKey)) {
            this.cache.delete(requestKey);
            console.log(`[ApiClient] Cleared cache for key: ${requestKey}`);
            return true;
        }
        return false;
    }

    /**
     * Legacy method for backward compatibility
     * @param {string} endpoint - The endpoint to clear cache for
     */
    clearCache(endpoint) {
        if (endpoint) {
            this.clearForEndpoint(endpoint);
        } else {
            this.clearAll();
        }
    }

    /**
     * Enable request deduplication
     */
    enableDeduplication() {
        this.deduplicationEnabled = true;
        console.log('[ApiClient] Request deduplication enabled');
    }

    /**
     * Disable request deduplication
     */
    disableDeduplication() {
        this.deduplicationEnabled = false;
        console.log('[ApiClient] Request deduplication disabled');
    }

    /**
     * Get the current count of pending requests
     * @returns {number} - Number of pending requests
     */
    getPendingRequestCount() {
        return this.pendingRequests.size;
    }

    /**
     * Debug method to log current request state
     * @param {string} context - Context for the debug log
     */
    debugRequestState(context = 'DEBUG') {
        const status = this.getDeduplicationStatus();
        debug(`[${context}] Current request state:`, {
            deduplicationEnabled: status.enabled,
            pendingRequests: status.pendingRequestCount,
            failedRequests: status.failedRequestCount,
            cacheSize: status.cacheSize,
            pendingDetails: status.pendingRequestsDetails
        });
    }

    /**
     * Get information about the current deduplication status
     * @returns {Object} - Deduplication status information
     */
    getDeduplicationStatus() {
        const pendingRequestsDetails = Array.from(this.pendingRequestsInfo.entries()).map(([key, info]) => ({
            requestKey: key,
            endpoint: info.endpoint,
            requestId: info.requestId,
            startTime: info.startTime,
            age: Date.now() - info.startTime,
            signalAborted: info.signal?.aborted || false,
            hasController: !!info.controller
        }));

        return {
            enabled: this.deduplicationEnabled,
            pendingRequestCount: this.pendingRequests.size,
            pendingRequests: Array.from(this.pendingRequests.keys()),
            pendingRequestsDetails,
            failedRequestCount: this.failedRequests.size,
            failedRequests: Array.from(this.failedRequests.keys()),
            cacheEnabled: this.cacheEnabled,
            cacheSize: this.cache.size
        };
    }

    // Specific API methods
    async getExams(options = {}) {
        console.log('VENDOR_CODE:', VENDOR_CODE)
        const params = new URLSearchParams({
            vendor_code: VENDOR_CODE
        });
        const endpoint = `/exam?${params}`;

        // Generate the request key to check if this is likely to be deduplicated
        const requestKey = this.generateRequestKey(endpoint, {
            ...options,
            timeout: 15000
        });

        // Log if this request might be deduplicated
        if (this.deduplicationEnabled && this.pendingRequests.has(requestKey)) {
            console.log(`[ApiClient] Exams request will be deduplicated`);
            console.log(`[ApiClient] Current pending requests: ${this.pendingRequests.size}`);
        } else {
            console.log(`[ApiClient] Fetching exams list`);
        }

        return this.request(endpoint, {
            ...options,
            timeout: 15000 // Longer timeout for initial load
        });
    }

    async getQnA(examCode, options = {}, is_free = false) {
        const params = new URLSearchParams({
            exam_code: examCode,
            is_free: is_free ? 'true' : 'false'
        });
        const endpoint = `/qna?${params}`;

        return this.request(endpoint, {
            ...options,
            timeout: options.timeout || this.defaultTimeout
        });
    }

    async getAIResponse(payload, options = {}) {
        const requestId = Math.random().toString(36).substring(2, 8);

        try {
            // Validate required fields
            if (!payload.userQuery && !payload.question) {
                throw new Error('Either userQuery or question must be provided');
            }

            // Format the payload with required question field
            const formattedPayload = {
                question: payload.question || payload.userQuery, // Required field
                ...payload
            };

            // Remove duplicate fields
            delete formattedPayload.userQuery;

            debug(`[${requestId}] AI request started with payload:`, {
                question: formattedPayload.question?.substring(0, 50) + '...',
                isInitialRequest: formattedPayload.isInitialRequest,
                questionId: formattedPayload.questionId,
                hasSystemPrompt: !!formattedPayload.systemPrompt,
                hasChatHistory: !!formattedPayload.chatHistory
            });

            // For AI requests, we might want to disable deduplication since each request
            // could be unique even with the same payload (e.g., different AI responses)
            const shouldDeduplicateAI = options.deduplicateAI !== false && this.deduplicationEnabled;

            // AI requests need special timeout handling:
            // - If external signal is provided (from AI Chat component), don't set our own timeout
            // - Otherwise, use a longer timeout for AI requests
            let aiTimeout;
            if (options.signal) {
                // External signal provided - let the caller handle timeout
                aiTimeout = options.timeout || 30000; // Generous fallback timeout
                debug(`[${requestId}] Using external signal with ${aiTimeout}ms fallback timeout`);
            } else {
                // No external signal - use our own timeout
                aiTimeout = options.timeout || Math.max(this.defaultTimeout, 20000);
                debug(`[${requestId}] Using internal timeout of ${aiTimeout}ms`);
            }

            // Use the standard request method with the appropriate timeout
            // This ensures consistent timeout handling across all request types
            const response = await this.request('/ai/askAiSimple', {
                method: 'POST',
                body: JSON.stringify(formattedPayload),
                timeout: aiTimeout,
                deduplicationEnabled: shouldDeduplicateAI,
                // Don't show the generic network error alert for AI requests
                // We'll show a more specific one in the catch block
                skipErrorAlert: true,
                ...options,
            });

            debug(`[${requestId}] AI request completed successfully`);
            console.log('[ApiClient] Raw AI response:', JSON.stringify(response));
            return response;
        } catch (error) {
            debug(`[${requestId}] AI request failed:`, {
                errorName: error.name,
                errorMessage: error.message,
                hasExternalSignal: !!options.signal,
                skipTimeoutAlert: !!options.skipTimeoutAlert
            });

            // Don't show alerts for external aborts (handled by AI Chat component)
            const isExternalAbort = error.name === 'AbortError' && options.signal;

            if (!isExternalAbort) {
                console.error('[ApiClient] AI request failed:', error);
            } else {
                debug(`[${requestId}] AI request aborted externally (likely by AI Chat timeout)`);
            }

            // Show a specific error message for AI requests only if not skipped and not external abort
            if (!isExternalAbort && (error.name === 'AbortError' || error.message.includes('timeout')) && !options.skipTimeoutAlert) {
                // Only show the alert if we're not already showing too many alerts
                if (!this._isShowingTooManyAlerts()) {
                    Alert.alert(
                        'AI Response Timeout',
                        'The AI is taking longer than expected to respond. Please try again with a simpler question.',
                        [{ text: 'OK', onPress: () => console.log('AI timeout alert closed') }]
                    );
                }
            }

            throw error;
        }
    }

    /**
     * User Authentication and Profile Management
     */

    /**
     * Authenticate user with Google ID token
     * @param {string} googleIdToken - Google ID token from Google Sign-In
     * @returns {Promise<Object>} - User data including authentication token
     */
    async loginWithGoogle(googleIdToken) {
        console.log('[ApiClient] Logging in with Google, token length:', googleIdToken ? googleIdToken.length : 0);
        debug('Logging in with Google');

        if (!googleIdToken) {
            console.error('[ApiClient] No Google ID token provided to loginWithGoogle');
            throw new Error('No Google ID token provided');
        }

        // Log the first and last 5 characters of the token for debugging (without exposing full token)
        console.log('[ApiClient] Google ID token (partial):',
            googleIdToken.substring(0, 5) + '...' + googleIdToken.substring(googleIdToken.length - 5));

        try {
            console.log('[ApiClient] Sending request to /auth/google endpoint');
            const requestBody = { idToken: googleIdToken };
            console.log('[ApiClient] Request body:', JSON.stringify({
                ...requestBody,
                idToken: requestBody.idToken.substring(0, 5) + '...' + requestBody.idToken.substring(requestBody.idToken.length - 5)
            }));

            const response = await this.request('/auth/google', {
                method: 'POST',
                body: JSON.stringify(requestBody),
                skipCache: true,
                timeout: 15000 // Longer timeout for auth requests
            });

            console.log('[ApiClient] Received response from /auth/google:', JSON.stringify(response, null, 2));
            console.log('response:', response);
            console.log('response type:', typeof response);
            console.log('response is null:', response === null);
            console.log('response is undefined:', response === undefined);

            // Check if we got a valid response
            if (!response) {
                console.error('[ApiClient] Received null/undefined response from server');
                throw new Error('No response received from server');
            }

            // Check if the response indicates success
            if (response.success === false) {
                console.error('[ApiClient] Server returned error:', response.message || 'Unknown error');
                throw new Error(response.message || 'Authentication failed');
            }

            // If login is successful, set the authentication token
            if (response && response.token) {
                // Log the token details for debugging
                console.log('[ApiClient] Auth token found in response, type:', typeof response.token,
                    'length:', response.token ? response.token.length : 0);

                // Make sure the token is a string
                const tokenStr = String(response.token);

                // Set the auth token
                this.setAuthToken(tokenStr);

                // Also store the token in AsyncStorage for persistence
                try {
                    await AsyncStorage.setItem('auth_token', tokenStr);
                    console.log('[ApiClient] Auth token stored in AsyncStorage');
                } catch (storageError) {
                    console.error('[ApiClient] Failed to store auth token in AsyncStorage:', storageError);
                }

                console.log('[ApiClient] Auth token set after Google login, token length:', tokenStr.length);
                debug('Auth token set after Google login');
            } else {
                console.error('[ApiClient] No auth token received from Google login');
                console.error('[ApiClient] Response structure:', {
                    hasResponse: !!response,
                    responseKeys: response ? Object.keys(response) : [],
                    hasToken: response && 'token' in response,
                    tokenValue: response ? response.token : 'N/A'
                });
                debug('No auth token received from Google login');
                throw new Error('Google login failed - no token in response');
            }

            return response;
        } catch (error) {
            console.error('[ApiClient] Google login failed with error:', error);
            debug('Google login failed:', error);
            throw error;
        }
    }

    /**
     * Logout user and invalidate token on server
     * @returns {Promise<Object>} - Logout confirmation
     */
    async logout() {
        debug('Logging out user');
        try {
            // Only attempt to call the logout endpoint if we have a custom auth token
            if (this.token !== API_TOKEN) {
                await this.request('/auth/logout', {
                    method: 'POST',
                    skipCache: true
                });
            }
        } catch (error) {
            debug('Error during logout API call:', error);
            // Continue with logout even if API call fails
        } finally {
            // Always reset to the default API token
            this.setAuthToken(null);

            // Also remove the token from AsyncStorage
            try {
                AsyncStorage.removeItem('auth_token');
                console.log('[ApiClient] Auth token removed from AsyncStorage during logout');
            } catch (storageError) {
                console.error('[ApiClient] Failed to remove auth token from AsyncStorage:', storageError);
            }
        }

        return { success: true };
    }

    /**
     * Get current user profile
     * @returns {Promise<Object>} - User profile data
     */
    async getUserProfile() {
        debug('Getting user profile');
        return this.request('/user/profile', {
            method: 'GET'
        });
    }

    /**
   * Get user by Google ID (requires JWT authentication)
   * @param {string} googleId - Google ID of the user
   * @returns {Promise<Object>} - Complete user object
   */
    async getUserByGoogleId(googleId) {
        debug(`Getting user by Google ID: ${googleId}`);
        if (!googleId) {
            throw new Error('Google ID is required');
        }
        return this.request(`/user/google/${encodeURIComponent(googleId)}`, {
            method: 'GET'
        });
    }

    /**
     * Update user profile
     * @param {Object} profileData - Profile data to update
     * @returns {Promise<Object>} - Updated user profile
     */
    async updateUserProfile(profileData) {
        debug('Updating user profile');
        return this.request('/user/profile', {
            method: 'PUT',
            body: JSON.stringify(profileData),
            skipCache: true
        });
    }

    /**
     * User Progress Context
     */

    /**
     * Get user progress for all courses or a specific course
     * @param {string} [courseId] - Optional course ID to filter progress
     * @returns {Promise<Object>} - User progress data
     */
    async getUserProgress(courseId) {
        const endpoint = courseId
            ? `/user/progress?courseId=${encodeURIComponent(courseId)}`
            : '/user/progress';

        debug(`Getting user progress${courseId ? ` for course ${courseId}` : ''}`);
        return this.request(endpoint, {
            method: 'GET'
        });
    }

    /**
     * Update user progress for exam questions
     * @param {string} examId - Exam ID (e.g., AIF-C01)
     * @param {string} subject - Subject name
     * @param {Object} progressData - Progress data to update
     * @param {string} progressData.questionId - Question ID
     * @param {string} progressData.action - Action type: 'browsed', 'bookmarked', or 'answered'
     * @param {boolean} [progressData.isBookmarked] - Required when action is 'bookmarked'
     * @param {boolean} [progressData.isCorrect] - Required when action is 'answered'
     * @returns {Promise<Object>} - Updated user progress
     */
    async updateUserProgress(examId, subject, progressData) {
        debug(`Updating user progress for exam ${examId}, subject ${subject}`);
        return this.request(`/user/progress/${encodeURIComponent(examId)}/${encodeURIComponent(subject)}`, {
            method: 'PUT',
            body: JSON.stringify(progressData),
            skipCache: true
        });
    }

    /**
     * Bulk update user progress for an exam according to new Swagger specification
     * Uses PUT /api/user/progress/{examId} endpoint
     * @param {string} examId - Exam ID
     * @param {Object} progressData - Progress data organized by subject in UserProgressContext format
     * @returns {Promise<Object>} - Updated progress data
     */
    async updateUserProgressBulk(examId, progressData) {
        // Validate required parameters
        if (!examId) {
            throw new Error('examId is required for bulk progress update');
        }

        if (!progressData || typeof progressData !== 'object') {
            throw new Error('progressData must be a valid object');
        }

        debug(`Bulk updating user progress for exam ${examId}`, {
            subjects: Object.keys(progressData).length,
            progressDataKeys: Object.keys(progressData)
        });

        // The progressData should already be in the correct format:
        // {
        //   "subject1": { browsed: [...], bookmarked: [...], correct: [...], incorrect: [...] },
        //   "subject2": { browsed: [...], bookmarked: [...], correct: [...], incorrect: [...] }
        // }

        const endpoint = `/user/progress/${encodeURIComponent(examId)}`;

        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(progressData),
            skipCache: true,
            timeout: 10000 // 10 second timeout for bulk operations
        });
    }

    /**
     * Quiz Result Context
     */

    /**
     * Get user quiz results
     * @param {string} [quizId] - Optional quiz ID to filter results
     * @returns {Promise<Object>} - User quiz results
     */
    async getQuizResults(quizId) {
        const endpoint = quizId
            ? `/user/quiz-results?quizId=${encodeURIComponent(quizId)}`
            : '/user/quiz-results';

        debug(`Getting user quiz results${quizId ? ` for quiz ${quizId}` : ''}`);
        return this.request(endpoint, {
            method: 'GET'
        });
    }

    /**
     * Submit a quiz result
     * @param {Object} quizResult - Quiz result data
     * @param {string} quizResult.quizId - Quiz ID
     * @param {number} quizResult.score - Score as percentage
     * @param {number} quizResult.totalQuestions - Total number of questions
     * @param {number} quizResult.correctAnswers - Number of correct answers
     * @param {number} quizResult.timeTaken - Time taken in seconds
     * @param {Array} quizResult.answers - Array of answer objects
     * @returns {Promise<Object>} - Saved quiz result
     */
    async submitQuizResult(quizResult) {
        debug(`Submitting quiz result for quiz ${quizResult.quizId}`);
        return this.request('/user/quiz-results', {
            method: 'POST',
            body: JSON.stringify(quizResult),
            skipCache: true
        });
    }

    /**
     * Purchase Context
     */

    /**
     * Get user purchases
     * @param {string} [status] - Optional status filter ('active', 'expired', 'refunded')
     * @returns {Promise<Object>} - User purchases
     */
    async getUserPurchases(status) {
        const endpoint = status
            ? `/user/purchases?status=${encodeURIComponent(status)}`
            : '/user/purchases';

        debug(`Getting user purchases${status ? ` with status ${status}` : ''}`);

        try {
            return await this.request(endpoint, {
                method: 'GET'
            });
        } catch (error) {
            // Check if this is an authentication error
            if (error.message.includes('401') || error.message.includes('403')) {
                console.error('[ApiClient] Authentication error in getUserPurchases - token may be expired:', {
                    currentTokenLength: this.token ? this.token.length : 0,
                    isDefaultToken: this.token === API_TOKEN,
                    endpoint: endpoint,
                    error: error.message
                });

                // Try to refresh the token from AsyncStorage
                try {
                    const storedGoogleToken = await AsyncStorage.getItem('google_id_token');
                    if (storedGoogleToken && storedGoogleToken !== this.token) {
                        console.log('[ApiClient] Found different Google token in storage, updating auth token');
                        this.setAuthToken(storedGoogleToken);

                        // Retry the request with the refreshed token
                        return await this.request(endpoint, {
                            method: 'GET'
                        });
                    }
                } catch (refreshError) {
                    console.error('[ApiClient] Failed to refresh token from storage:', refreshError);
                }
            }

            throw error;
        }
    }

    /**
     * Verify and record a purchase
     * @param {Object} purchaseData - Purchase data
     * @param {string} purchaseData.productId - Product ID
     * @param {string} purchaseData.transactionId - Transaction ID
     * @param {string} purchaseData.platform - Platform ('ios', 'android', 'web')
     * @param {string} purchaseData.receipt - Purchase receipt for verification
     * @returns {Promise<Object>} - Verified purchase data
     */
    async verifyPurchase(purchaseData) {
        debug(`Verifying purchase for product ${purchaseData.productId}`);
        return this.request('/user/purchases/verify', {
            method: 'POST',
            body: JSON.stringify(purchaseData),
            skipCache: true
        });
    }

    /**
     * AI Credit Context
     */

    /**
     * Get user AI credits
     * @returns {Promise<Object>} - User AI credits data
     */
    async getAICredits() {
        debug('Getting user AI credits');
        return this.request('/user/ai-credits', {
            method: 'GET'
        });
    }

    /**
     * Get AI credit transaction history
     * @param {number} [limit=20] - Number of transactions to return
     * @param {number} [offset=0] - Offset for pagination
     * @returns {Promise<Object>} - AI credit transactions
     */
    async getAICreditTransactions(limit = 20, offset = 0) {
        debug(`Getting AI credit transactions (limit: ${limit}, offset: ${offset})`);
        return this.request(`/user/ai-credits/transactions?limit=${limit}&offset=${offset}`, {
            method: 'GET'
        });
    }

    /**
     * Purchase AI credits
     * @param {Object} purchaseData - Purchase data
     * @param {number} purchaseData.amount - Amount of credits to purchase
     * @param {string} purchaseData.paymentMethod - Payment method
     * @param {Object} purchaseData.paymentDetails - Payment details
     * @returns {Promise<Object>} - Updated AI credits
     */
    async purchaseAICredits(purchaseData) {
        debug(`Purchasing ${purchaseData.amount} AI credits`);
        return this.request('/user/ai-credits/purchase', {
            method: 'POST',
            body: JSON.stringify(purchaseData),
            skipCache: true
        });
    }

    /**
     * AI Chat Context
     */

    /**
     * Get user AI chat history
     * Returns the complete AI chat history organized by question IDs
     * @returns {Promise<Object>} - Complete AI chat history in the format:
     * {
     *   "questionId1": {
     *     "messages": [
     *       {
     *         "id": "message-id",
     *         "choices": [
     *           {
     *             "index": 0,
     *             "message": {
     *               "role": "user|assistant",
     *               "content": "message content",
     *               "isLoading": false,
     *               "metadata": { ... }
     *             },
     *             "logprobs": null,
     *             "finish_reason": "stop"
     *           }
     *         ]
     *       }
     *     ],
     *     "quickReplies": ["reply1", "reply2", "reply3"]
     *   }
     * }
     */
    async getAIChatHistory() {
        debug('Getting complete AI chat history');
        return this.request('/user/ai-chats', {
            method: 'GET'
        });
    }

    /**
     * Update user AI chat history
     * Replaces the entire AI chat history with the provided data
     * @param {Object} chatHistoryData - Complete AI chat history data organized by question IDs
     * @param {Object} chatHistoryData[questionId] - Chat data for a specific question
     * @param {Array} chatHistoryData[questionId].messages - Array of message objects
     * @param {Array} chatHistoryData[questionId].quickReplies - Array of quick reply strings
     * @returns {Promise<Object>} - Updated AI chat history
     * @example
     * // Example usage:
     * const chatHistory = {
     *   "679e05c962fb0d0bbe484926": {
     *     "messages": [
     *       {
     *         "id": "welcome-message",
     *         "choices": [
     *           {
     *             "index": 0,
     *             "message": {
     *               "role": "assistant",
     *               "content": "I'm here to help with this Q&A question. What would you like to know?",
     *               "isLoading": false
     *             }
     *           }
     *         ]
     *       }
     *     ],
     *     "quickReplies": [
     *       "Explain why the answer is correct and others are wrong.",
     *       "Give an example to clarify the answer.",
     *       "Share references for the correct answer."
     *     ]
     *   }
     * };
     * await apiClient.updateAIChatHistory(chatHistory);
     *
     * // For incremental updates, use addAIChatHistory instead:
     * await apiClient.addAIChatHistory(examId, qnaId, messages, quickReplies);
     */
    async updateAIChatHistory(chatHistoryData) {
        debug('Updating complete AI chat history');
        return this.request('/user/ai-chats', {
            method: 'PUT',
            body: JSON.stringify(chatHistoryData),
            skipCache: true
        });
    }

    /**
     * Get a specific AI chat
     * @param {string} chatId - Chat ID
     * @returns {Promise<Object>} - Chat data including messages
     */
    async getAIChat(chatId) {
        debug(`Getting AI chat ${chatId}`);
        return this.request(`/user/ai-chats/${encodeURIComponent(chatId)}`, {
            method: 'GET'
        });
    }

    /**
     * Create a new AI chat
     * @param {string} [title] - Optional chat title
     * @returns {Promise<Object>} - New chat data
     */
    async createAIChat(title) {
        debug('Creating new AI chat');
        return this.request('/user/ai-chats', {
            method: 'POST',
            body: JSON.stringify({ title }),
            skipCache: true
        });
    }

    /**
     * Send a message in an AI chat
     * @param {string} chatId - Chat ID
     * @param {string} message - User message
     * @returns {Promise<Object>} - Updated chat with AI response
     */
    async sendAIChatMessage(chatId, message) {
        debug(`Sending message to AI chat ${chatId}`);
        return this.request(`/user/ai-chats/${encodeURIComponent(chatId)}/messages`, {
            method: 'POST',
            body: JSON.stringify({ message }),
            skipCache: true,
            timeout: 30000 // Longer timeout for AI responses
        });
    }

    /**
     * Delete an AI chat
     * @param {string} chatId - Chat ID
     * @returns {Promise<Object>} - Deletion confirmation
     */
    async deleteAIChat(chatId) {
        debug(`Deleting AI chat ${chatId}`);
        return this.request(`/user/ai-chats/${encodeURIComponent(chatId)}`, {
            method: 'DELETE',
            skipCache: true
        });
    }

    /**
     * Login Context
     */

    /**
     * Get user login history
     * @param {number} [limit=10] - Number of login records to return
     * @param {number} [offset=0] - Offset for pagination
     * @returns {Promise<Object>} - User login history
     */
    async getLoginHistory(limit = 10, offset = 0) {
        debug(`Getting login history (limit: ${limit}, offset: ${offset})`);
        return this.request(`/user/login-history?limit=${limit}&offset=${offset}`, {
            method: 'GET'
        });
    }

    /**
     * Add messages to AI chat history for a specific exam and QnA
     * @param {string} examId - Exam ID (e.g., "aws-certified-ai-practitioner-aif-c01")
     * @param {string} qnaId - QnA ID (e.g., "679e05c962fb0d0bbe484926")
     * @param {Array} messages - Array of message objects to append
     * @param {Array} quickReplies - Optional array of quick reply suggestions
     * @returns {Promise} API response with message count information
     */
    async addAIChatHistory(examId, qnaId, messages, quickReplies = null) {
        debug(`Adding AI chat messages for exam: ${examId}, qna: ${qnaId}, messageCount: ${messages?.length || 0}`);

        try {
            // Validate required parameters
            if (!examId || typeof examId !== 'string') {
                throw new Error('examId is required and must be a string');
            }

            if (!qnaId || typeof qnaId !== 'string') {
                throw new Error('qnaId is required and must be a string');
            }

            if (!messages || !Array.isArray(messages) || messages.length === 0) {
                throw new Error('messages is required and must be a non-empty array');
            }

            // Validate examId format
            if (!examId.match(/^[a-z0-9-]+$/)) {
                throw new Error('Invalid examId format. Expected format: aws-certified-ai-practitioner-aif-c01');
            }

            // Validate qnaId format
            if (!qnaId.match(/^[a-f0-9]{24}$/)) {
                throw new Error('Invalid qnaId format. Expected 24-character hexadecimal string');
            }

            // Validate message structure
            for (let i = 0; i < messages.length; i++) {
                const message = messages[i];
                if (!message.id || !message.choices || !Array.isArray(message.choices)) {
                    throw new Error(`Message at index ${i} must have id and choices array`);
                }

                for (let j = 0; j < message.choices.length; j++) {
                    const choice = message.choices[j];
                    if (!choice.message || !choice.message.role || !choice.message.content) {
                        throw new Error(`Choice at index ${j} in message ${i} must have message with role and content`);
                    }

                    if (!['user', 'assistant'].includes(choice.message.role)) {
                        throw new Error(`Message role must be either "user" or "assistant" in message ${i}, choice ${j}`);
                    }
                }
            }

            // Prepare request body
            const requestBody = {
                messages: messages
            };

            // Add quickReplies if provided
            if (quickReplies && Array.isArray(quickReplies)) {
                requestBody.quickReplies = quickReplies;
            }

            console.log(`[ApiClient] Sending ${messages.length} messages to AI chat history for ${examId}/${qnaId}`);

            const response = await this.request(`/user/ai-chats/${examId}/${qnaId}/messages`, {
                method: 'PUT',
                body: JSON.stringify(requestBody),
                skipCache: true
            });

            console.log(`[ApiClient] Successfully added ${messages.length} messages to AI chat history for ${examId}/${qnaId}`);

            return response;
        } catch (error) {
            console.error('[ApiClient] Error in addAIChatHistory:', error);
            throw error;
        }
    }


    // QnA Feedback
    /**
     * Submit feedback about a QnA item
     * @param {string} userId - ID of the user submitting feedback
     * @param {string} qnaId - ID of the QnA item being feedbacked
     * @param {string} content - Feedback content
     * @returns {Promise<Object>} - Response from server
     */
    async submitQnAFeedback(userId, qnaId, content) {
        debug(`Submitting QnA feedback for qnaId: ${qnaId}`);

        // Validate required parameters
        if (!userId || typeof userId !== 'string') {
            throw new Error('userId is required and must be a string');
        }
        if (!qnaId || typeof qnaId !== 'string') {
            throw new Error('qnaId is required and must be a string');
        }
        if (!content || typeof content !== 'string') {
            throw new Error('content is required and must be a string');
        }

        return this.request('/qna/feedback', {
            method: 'POST',
            body: JSON.stringify({ userId, qnaId, content }),
            skipCache: true,
            timeout: 10000 // 10 second timeout
        });
    }
}

const apiClient = new ApiClient();

export default apiClient;