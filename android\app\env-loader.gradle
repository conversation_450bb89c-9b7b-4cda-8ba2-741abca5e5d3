/**
 * Environment Variable Loader for React Native Monorepo
 * 
 * This script loads environment variables from the appropriate .env file
 * based on the build variant and build type, making them available to
 * the Gradle build system.
 */

def loadEnvFile(String envFilePath) {
    def envFile = file("../../${envFilePath}")
    def envProps = new Properties()
    
    if (envFile.exists()) {
        println "Loading environment from: ${envFilePath}"
        envFile.withInputStream { stream ->
            envProps.load(stream)
        }
    } else {
        println "Warning: Environment file not found: ${envFilePath}"
    }
    
    return envProps
}

def getEnvFilePath(String variant, String buildType) {
    // Determine the correct .env file based on variant and build type
    if (buildType == 'debug') {
        if (variant == 'pmi') {
            return '.env.pmi.dev'
        } else {
            return '.env.dev'
        }
    } else {
        if (variant == 'pmi') {
            return '.env.pmi'
        } else {
            return '.env'
        }
    }
}

def loadEnvironmentVariables() {
    def envVars = [:]
    
    // Try to determine variant from gradle task name or system properties
    def taskNames = gradle.startParameter.taskNames
    def variant = 'aws' // default
    def buildType = 'release' // default
    
    // Parse task names to determine variant and build type
    taskNames.each { taskName ->
        def lowerTaskName = taskName.toLowerCase()
        if (lowerTaskName.contains('pmi')) {
            variant = 'pmi'
        }
        if (lowerTaskName.contains('debug')) {
            buildType = 'debug'
        }
    }
    
    // Check system properties for overrides (from package.json scripts)
    if (System.getProperty('app.variant')) {
        variant = System.getProperty('app.variant')
    }
    if (System.getProperty('app.env')) {
        buildType = System.getProperty('app.env') == 'dev' ? 'debug' : 'release'
    }
    
    // Check environment variables for overrides (highest priority)
    if (System.getenv('APP_VARIANT')) {
        variant = System.getenv('APP_VARIANT')
    }
    if (System.getenv('APP_ENV')) {
        buildType = System.getenv('APP_ENV') == 'dev' ? 'debug' : 'release'
    }
    
    println "Detected variant: ${variant}, buildType: ${buildType}"
    
    // Load environment variables for the detected configuration
    def envFilePath = getEnvFilePath(variant, buildType)
    def envProps = loadEnvFile(envFilePath)
    
    // Convert Properties to Map and return
    envProps.each { key, value ->
        envVars[key.toString()] = value.toString()
    }
    
    // If no environment variables were loaded, try to load default .env
    if (envVars.isEmpty()) {
        println "No environment variables loaded, trying default .env file"
        def defaultEnvProps = loadEnvFile('.env')
        defaultEnvProps.each { key, value ->
            envVars[key.toString()] = value.toString()
        }
    }
    
    return envVars
}

// Load environment variables and make them available globally
ext.envVars = loadEnvironmentVariables()

// Helper function to get environment variable with fallback
ext.getEnvVar = { String key, String defaultValue = null ->
    return ext.envVars.get(key, defaultValue)
}

// Print loaded environment variables for debugging
println "Loaded environment variables:"
ext.envVars.each { key, value ->
    if (key.contains('PASSWORD') || key.contains('KEY') || key.contains('SECRET')) {
        println "  ${key}=***HIDDEN***"
    } else {
        println "  ${key}=${value}"
    }
}