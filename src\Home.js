import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Image } from 'react-native';
import { Text, Button, useTheme, Appbar } from 'react-native-paper';
import AICreditBadge from './components/AICreditBadge';
import AICreditModal from './components/AICreditModal';
import { useNavigation } from '@react-navigation/native';
import CircularProgress from 'react-native-circular-progress-indicator';
import CalendarPicker from 'react-native-calendar-picker';
import BuyNowModal from './components/BuyNowModal';
import EmptyStateCard from './components/EmptyStateCard';
import { useExamContext } from './store/ExamContext';
import { useUserProgress } from './store/UserProgressContext';
import { useQnAContext } from './store/QnAContext';
import { useFocusEffect } from '@react-navigation/native';
import { usePurchase } from './store/PurchaseContext';
import UpgradeButton from './components/UpgradeButton';
import StickyBottomAdMob from './components/StickyBottomAdMob';
import { useSubscriptionRefresh } from './utils/subscriptionUtils';
import FixedFontButton from './components/FixedFontButton';
import CustomAppbarContent from './components/CustomAppbarContent';
import { IS_UNLOCKED } from '@env';

const Home = () => {
  const { questionsBySubject } = useQnAContext();
  const { progress } = useUserProgress();
  const { selectedExam } = useExamContext();
  const { subscriptionActive } = usePurchase();

  // Calculate total number of questions
  const totalQnA = useMemo(() => {
    return Object.values(questionsBySubject).reduce(
      (acc, questions) => acc + questions.length,
      0
    );
  }, [questionsBySubject]);

  // State to store progress metrics
  const [progressMetrics, setProgressMetrics] = useState({
    totalBrowsed: 0,
    totalCorrect: 0,
    totalQuizQuestions: 0,
    accuracy: 0,
    qnaCompletion: 0
  });

  const processProgressData = () => {
    const activities = {};
    const examId = selectedExam?.id;

    if (!examId || !progress[examId]) return activities;

    Object.entries(progress[examId]).forEach(([subject, subjectData]) => {
      // Process QnA activities
      subjectData.browsed?.forEach(({ timestamp }) => {
        const dateKey = new Date(timestamp).toISOString().split('T')[0];
        activities[dateKey] = activities[dateKey] || {
          qna: { num_q: 0, subjects: new Set() },
          quiz: { num_q: 0, correct: 0, incorrect: 0, subjects: new Set() }
        };
        activities[dateKey].qna.num_q += 1;
        activities[dateKey].qna.subjects.add(subject);
      });

      // Process Correct answers
      subjectData.correct?.forEach(({ timestamp }) => {
        const dateKey = new Date(timestamp).toISOString().split('T')[0];
        activities[dateKey] = activities[dateKey] || {
          qna: { num_q: 0, subjects: new Set() },
          quiz: { num_q: 0, correct: 0, incorrect: 0, subjects: new Set() }
        };
        activities[dateKey].quiz.num_q += 1;
        activities[dateKey].quiz.correct += 1;
        activities[dateKey].quiz.subjects.add(subject);
      });

      // Process Incorrect answers
      subjectData.incorrect?.forEach(({ timestamp }) => {
        const dateKey = new Date(timestamp).toISOString().split('T')[0];
        activities[dateKey] = activities[dateKey] || {
          qna: { num_q: 0, subjects: new Set() },
          quiz: { num_q: 0, correct: 0, incorrect: 0, subjects: new Set() }
        };
        activities[dateKey].quiz.num_q += 1;
        activities[dateKey].quiz.incorrect += 1;
        activities[dateKey].quiz.subjects.add(subject);
      });
    });

    // Convert Sets to Arrays
    Object.values(activities).forEach(activity => {
      if (activity.qna) activity.qna.subjects = Array.from(activity.qna.subjects);
      if (activity.quiz) activity.quiz.subjects = Array.from(activity.quiz.subjects);
    });

    return activities;
  };

  const activitiesByDate = processProgressData();

  // Calculate progress metrics
  const calculateProgressMetrics = useCallback(() => {
    let totalBrowsed = 0;
    let totalCorrect = 0;
    let totalIncorrect = 0;
    const examId = selectedExam?.id;

    if (examId && progress[examId]) {
      Object.values(progress[examId]).forEach(subjectData => {
        totalBrowsed += subjectData.browsed?.length || 0;
        totalCorrect += subjectData.correct?.length || 0;
        totalIncorrect += subjectData.incorrect?.length || 0;
      });
    }

    const totalQuizQuestions = totalCorrect + totalIncorrect;
    const accuracy = totalQuizQuestions > 0
      ? Math.round((totalCorrect / totalQuizQuestions) * 100)
      : 0;

    // Calculate qnaCompletion here with the correct totalBrowsed
    const qnaCompletion = totalQnA > 0
      ? Math.round((totalBrowsed / totalQnA) * 100)
      : 0;

    // Update the state with the new metrics
    setProgressMetrics({
      totalBrowsed,
      totalCorrect,
      totalQuizQuestions,
      accuracy,
      qnaCompletion, // Include in return object
    });

    return {
      totalBrowsed,
      totalCorrect,
      totalIncorrect,
      totalQuizQuestions,
      accuracy,
      qnaCompletion
    };
  }, [subscriptionActive, progress, selectedExam, totalQnA]);

  useSubscriptionRefresh();

  useFocusEffect(
    useCallback(() => {
      calculateProgressMetrics();
    }, [])
  );

  const [isBuyNowModalVisible, setIsBuyNowModalVisible] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const { colors } = useTheme();
  const [selectedStartDate, setSelectedStartDate] = useState(null);
  const [selectedEndDate, setSelectedEndDate] = useState(null);
  const [summaryText, setSummaryText] = useState('');
  const navigation = useNavigation();
  const [calendarKey, setCalendarKey] = useState(0);
  const [hasActivities, setHasActivities] = useState(false);
  const [creditModalVisible, setCreditModalVisible] = useState(false);

  const currentDate = new Date();

  const formatDate = (date) => date.toISOString().split('T')[0];

  const onDateChange = (date, type) => {
    if (type === 'START_DATE') setSelectedStartDate(date);
    else setSelectedEndDate(date);
  };

  useEffect(() => {
    const { text, hasActivities } = calculateSummary();
    setSummaryText(text);
    setHasActivities(hasActivities);
  }, [selectedStartDate, selectedEndDate]);

  useFocusEffect(
    useCallback(() => {
      // Reset calendar selections
      setSelectedStartDate(null);
      setSelectedEndDate(null);
      setCalendarKey(prev => prev + 1);

      // Recalculate summary with default dates
      const { text, hasActivities } = calculateSummary();
      setSummaryText(text);
      setHasActivities(hasActivities);
    }, [])
  );

  const formatDisplayDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  const getCalendarText = () => {
    if (!selectedStartDate && !selectedEndDate) {
      return 'Tap any date to start selecting';
    }
    const start = selectedStartDate;
    const end = selectedEndDate || start;
    return start === end || !selectedEndDate
      ? `Viewing: ${formatDisplayDate(start)}`
      : `${formatDisplayDate(start)} - ${formatDisplayDate(end)}`;
  };

  const calculateSummary = () => {
    let qnaTotal = 0, quizTotal = 0, quizCorrect = 0;
    let qnaSubjects = new Set(), quizSubjects = new Set();

    Object.entries(activitiesByDate).forEach(([date, data]) => {
      const formattedDate = formatDate(new Date(date));
      const startDate = selectedStartDate;
      const endDate = selectedEndDate || startDate;

      const shouldInclude = !startDate && !endDate
        ? true
        : formattedDate >= formatDate(startDate) &&
        formattedDate <= formatDate(endDate);

      if (shouldInclude) {
        if (data.qna) {
          qnaTotal += data.qna.num_q;
          data.qna.subjects.forEach(subj => qnaSubjects.add(subj));
        }
        if (data.quiz) {
          quizTotal += data.quiz.num_q;
          quizCorrect += data.quiz.correct;
          data.quiz.subjects.forEach(subj => quizSubjects.add(subj));
        }
      }
    });

    const hasSelectedDates = selectedStartDate || selectedEndDate;
    const hasActivitiesInPeriod = (qnaTotal + quizTotal) > 0;
    const quizAccuracy = quizTotal > 0
      ? Math.round((quizCorrect / quizTotal) * 100)
      : 0;

    let text = '';
    let hasActivities = true;

    if (hasSelectedDates) {
      if (hasActivitiesInPeriod) {
        const lines = [];
        if (qnaTotal > 0) {
          lines.push(`• Reviewed ${qnaTotal} Q&A`);
        }
        if (quizTotal > 0) {
          lines.push(`• Answered ${quizTotal} questions: ${quizCorrect} correct (${quizAccuracy}%)`);
        }
        text = lines.join('\n');
      } else {
        text = 'No activity found for selected dates.';
      }
      hasActivities = hasActivitiesInPeriod;
    } else {
      if (qnaTotal > 0 || quizTotal > 0) {
        const lines = [];
        if (qnaTotal > 0) {
          lines.push(`• Reviewed ${qnaTotal} Q&A`);
        }
        if (quizTotal > 0) {
          lines.push(`• Answered ${quizTotal} questions: ${quizCorrect} correct (${quizAccuracy}%)`);
        }
        text = `This month's progress:\n${lines.join('\n')}`;
      } else {
        text = 'No study activity recorded this month yet.';
      }
    }

    return {
      text,
      hasActivities: hasSelectedDates ? hasActivitiesInPeriod : (qnaTotal + quizTotal) > 0
    };
  };


  const handleResetDates = () => {
    setSelectedStartDate(null);
    setSelectedEndDate(null);
    setCalendarKey(prev => prev + 1);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Appbar.Header elevated>
        {/* App icon on the left */}
        <View style={{ marginLeft: 10, marginRight: 4 }}>
          <Image
            source={require('../android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png')}
            style={{ width: 28, height: 28 }}
          />
        </View>

        {/* Show exam_code instead of exam_name */}
        <CustomAppbarContent
          title={selectedExam?.exam_code || ""}
          titleStyle={{
            paddingHorizontal: 12
          }}
        />
        <FixedFontButton
          mode="text"
          onPress={() => navigation.navigate('Welcome2')}
          labelStyle={{ color: colors.primary, fontWeight: '600', fontSize: 14 }}
          compact
          style={{ marginRight: 8 }}
        >
          Browse Exams
        </FixedFontButton>

        <AICreditBadge onPress={() => setCreditModalVisible(true)} />

      </Appbar.Header>
      {!selectedExam ? (
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <EmptyStateCard
            onAction={() => navigation.navigate('Welcome2')}
            subtitle="Please select an exam to view study materials"
          />
        </ScrollView>
      ) : (
        <>
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <View style={styles.innerContainer}>
              <View style={[styles.progressSection, { backgroundColor: colors.surface }]}>
                <View style={styles.progressContainer}>
                  <View style={styles.circleContainer}>
                    <CircularProgress
                      value={progressMetrics.qnaCompletion}
                      valueSuffix="%"
                      radius={45}
                      textColor={colors.onSurface}
                      activeStrokeColor={colors.primary}
                    />
                    <Text style={[styles.progressText, { color: colors.onSurface }]} maxFontSizeMultiplier={1.5}>
                      {progressMetrics.totalBrowsed} / {totalQnA} Q&A Reviewed
                    </Text>
                  </View>

                  <View style={[styles.separator, { backgroundColor: colors.outline }]} />

                  {/* Accuracy circle */}
                  <View style={styles.circleContainer}>
                    <CircularProgress
                      value={progressMetrics.accuracy}
                      valueSuffix="%"
                      radius={45}
                      textColor={colors.onSurface}
                      activeStrokeColor={colors.primary}
                    />
                    <Text style={[styles.progressText, { color: colors.onSurface }]} maxFontSizeMultiplier={1.5}>
                      {progressMetrics.totalCorrect} / {progressMetrics.totalQuizQuestions} Correct in Quiz
                    </Text>
                  </View>
                </View>

                {!subscriptionActive && !IS_UNLOCKED && (
                  <FixedFontButton
                    mode="contained-tonal"
                    style={styles.upgradeButton}
                    labelStyle={{ fontWeight: '600', fontSize: 16 }}
                    onPress={() => setIsBuyNowModalVisible(true)}
                  >
                    Upgrade to Unlock All Features
                  </FixedFontButton>
                )}
              </View>
              {/* Action Buttons */}
              <View style={styles.actionContainer}>
                <FixedFontButton
                  mode="contained"
                  icon='book'
                  contentStyle={styles.buttonContent}
                  style={styles.actionButton}
                  labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
                  theme={{ colors: { primary: colors.primary } }}
                  onPress={() => navigation.navigate('Q&A')}
                >
                  Revise Q&A
                </FixedFontButton>

                <FixedFontButton
                  mode="contained"
                  icon="play"
                  contentStyle={styles.buttonContent}
                  style={styles.actionButton}
                  labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
                  theme={{ colors: { primary: colors.primary } }}
                  onPress={() => navigation.navigate('Quiz')}
                >
                  Take Quiz
                </FixedFontButton>
              </View>
              {/* Calendar Section */}
              <View style={[styles.calendarSection, { backgroundColor: colors.surface }]}>
                <View style={styles.calendarHeader}>
                  <Text style={[styles.sectionTitle, { color: colors.onSurface }]}>
                    Study Calendar
                  </Text>

                  <View style={styles.calendarStatus}>
                    <Text style={[styles.calendarHint, { color: colors.onSurfaceVariant }]} maxFontSizeMultiplier={1.5}>
                      {getCalendarText()}
                    </Text>
                    {(selectedStartDate || selectedEndDate) && (
                      <FixedFontButton
                        mode="text"
                        onPress={handleResetDates}
                        labelStyle={{ color: colors.primary, marginLeft: 8 }}
                        compact
                      >
                        Clear
                      </FixedFontButton>
                    )}
                  </View>
                </View>

                <View style={styles.calendarWrapper}>
                  <CalendarPicker
                    key={calendarKey}
                    allowRangeSelection={true}
                    allowBackwardRangeSelect={true}
                    enableDateChange={true}
                    maxDate={currentDate}
                    onDateChange={onDateChange}
                    restrictMonthNavigation={true}
                    selectedDayColor={colors.primary}
                    selectedDayTextColor={colors.onPrimary}
                    todayBackgroundColor={colors.surfaceVariant}
                    todayTextStyle={{ color: colors.onSurfaceVariant }}
                    textStyle={{ color: colors.onSurface }}
                    previousComponent={
                      <Text style={[styles.calendarNav, { color: colors.primary }]}>‹</Text>
                    }
                    nextComponent={
                      <Text style={[styles.calendarNav, { color: colors.primary }]}>›</Text>
                    }
                    previousTitleStyle={{ marginLeft: 8 }}
                    nextTitleStyle={{ marginRight: 8 }}
                    monthTitleStyle={{
                      color: colors.onSurface,
                      fontWeight: '600',
                      marginLeft: 8
                    }}
                    yearTitleStyle={{
                      color: colors.onSurfaceVariant,
                      marginRight: 8
                    }}
                    dayShape="circle"
                    customDatesStyles={(date) => {
                      const dateKey = date.toISOString().split('T')[0];
                      const dailyData = activitiesByDate[dateKey];
                      if (dailyData) {
                        const hasQna = dailyData.qna?.num_q > 0;
                        const hasQuiz = dailyData.quiz?.num_q > 0;
                        return {
                          textStyle: {
                            fontWeight: hasQna ? 'bold' : 'normal',
                            textDecorationLine: hasQuiz ? 'underline' : 'none',
                            color: colors.onSurface,
                          },
                        };
                      }
                      return {};
                    }}
                  />
                </View>

                <View style={styles.calendarFooter}>
                  <Text style={[styles.legendText, { color: colors.onSurfaceVariant }]} maxFontSizeMultiplier={1.2}>
                    <Text style={{ fontWeight: 'bold' }}>Bold</Text>: Q&A days{'  '}
                    <Text style={{ textDecorationLine: 'underline' }}>Underline</Text>: Quiz days
                  </Text>
                </View>
              </View>

              {/* Summary Section */}
              <View style={[styles.summarySection, { backgroundColor: colors.surface }]}>
                <Text style={[styles.sectionTitle, { color: colors.onSurface }]}>
                  Study Summary
                </Text>
                <Text style={[styles.infoText, { color: colors.onSurfaceVariant }]}>
                  {summaryText}
                </Text>
              </View>

              {!subscriptionActive && (
                <UpgradeButton onPress={() => setIsBuyNowModalVisible(true)} />
              )}

              {/* {!subscriptionActive && (
                AdmobService.renderBannerAdContainer(
                  BannerAdSize.BANNER,
                  (error) => console.log('Banner ad failed to load:', error)
                )
              )} */}

            </View>
            <BuyNowModal
              buyNowModalVisible={isBuyNowModalVisible}
              setBuyNowModalVisible={setIsBuyNowModalVisible}
              selectedProduct={selectedExam}
              selectedPlan={selectedPlan}
              setSelectedPlan={setSelectedPlan}
              onPurchaseComplete={async (result) => {
                console.log('[Home] Purchase completed, refreshing subscription status');
                await refreshMetricsAndSubscription();
              }}
            />
          </ScrollView>
        </>
      )}
      <AICreditModal
        visible={creditModalVisible}
        onDismiss={() => setCreditModalVisible(false)}
      />
      <StickyBottomAdMob subscriptionActive={subscriptionActive} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bannerContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    paddingHorizontal: 16,
  },
  innerContainer: {
    padding: 16,
  },
  progressSection: {
    borderRadius: 16,
    padding: 20,
    elevation: 2,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start', // Changed from 'center'
    marginBottom: 20,
  },
  separator: {
    width: 1,
    height: 60, // Reduced height to match circle size
    marginHorizontal: 8,
    marginTop: 24, // Align with circle center
  },
  circleContainer: {
    alignItems: 'center',
    flex: 1,
    marginTop: 8, // Add top margin for better spacing
  },
  progressText: {
    fontSize: 13,
    textAlign: 'center',
    marginTop: 12,
    lineHeight: 16,
  },
  upgradeButton: {
    borderRadius: 8,
    width: '100%',
  },
  calendarSection: {
    borderRadius: 16,
    padding: 16,
    elevation: 2,
  },
  calendarHeader: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  calendarStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  calendarHint: {
    fontSize: 14,
    flexShrink: 1,
    flex: 1,
  },
  calendarWrapper: {
    marginHorizontal: -4,
  },
  calendarNav: {
    fontSize: 24,
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  calendarFooter: {
    marginTop: 16,
    alignItems: 'center',
  },
  legendText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  summarySection: {
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    marginVertical: 10,
  },
  infoText: {
    fontSize: 15,
    lineHeight: 22,
  },
  actionContainer: {
    marginVertical: 10,
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'space-between'
  },
  actionButton: {
    borderRadius: 8,
    width: '48%',
  },
  buttonContent: {
    height: 48,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 30,
  },
});

export default Home;