import { Platform, View, StyleSheet } from 'react-native';
import {
  BannerAd,
  BannerAdSize,
  TestIds,
  InterstitialAd,
  AdEventType,
  RewardedAd,
  RewardedAdEventType,
} from 'react-native-google-mobile-ads';
import {
  ADMOB_ANDROID_BANNER_ID,
  ADMOB_ANDROID_INTERSTITIAL_ID,
  ADMOB_ANDROID_REWARDED_ID,
  ADMOB_IOS_BANNER_ID,
  ADMOB_IOS_INTERSTITIAL_ID,
  ADMOB_IOS_REWARDED_ID
} from '@env';

const adUnitIds = {
  banner: Platform.select({
    ios: __DEV__ ? TestIds.BANNER : ADMOB_IOS_BANNER_ID,
    android: __DEV__ ? TestIds.BANNER : ADMOB_ANDROID_BANNER_ID,
  }),
  interstitial: Platform.select({
    ios: __DEV__ ? TestIds.INTERSTITIAL : ADMOB_IOS_INTERSTITIAL_ID,
    android: __DEV__ ? TestIds.INTERSTITIAL : ADMOB_ANDROID_INTERSTITIAL_ID,
  }),
  rewarded: Platform.select({
    ios: __DEV__ ? TestIds.REWARDED : ADMOB_IOS_REWARDED_ID,
    android: __DEV__ ? TestIds.REWARDED : ADMOB_ANDROID_REWARDED_ID,
  }),
};

class AdmobService {
  constructor() {
    this.interstitialAd = null;
    this.rewardedAd = null;
    this.initAds();
  }

  initAds() {
    // Initialize interstitial ad
    this.interstitialAd = InterstitialAd.createForAdRequest(adUnitIds.interstitial, {
      requestNonPersonalizedAdsOnly: true,
    });

    // Initialize rewarded ad
    this.rewardedAd = RewardedAd.createForAdRequest(adUnitIds.rewarded, {
      requestNonPersonalizedAdsOnly: true,
    });
  }

  // Banner Ad Component
  renderBannerAd(size = BannerAdSize.BANNER, onAdFailedToLoad) {
    return (
      <BannerAd
        unitId={adUnitIds.banner}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: true,
        }}
        onAdFailedToLoad={onAdFailedToLoad}
      />
    );
  }

  // Reusable Banner Ad Container with default styling
  renderBannerAdContainer(size = BannerAdSize.BANNER, onAdFailedToLoad) {
    const styles = this.constructor.getStyles();
    return (
      <View style={styles.bannerContainer}>
        {this.renderBannerAd(size, (error) => {
          console.log('Banner ad failed to load:', error);
          onAdFailedToLoad?.(error);
        })}
      </View>
    );
  }

  // Styles for banner container
  static getStyles() {
    return StyleSheet.create({
      bannerContainer: {
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'transparent',
        paddingHorizontal: 16,
      },
    });
  }

  // Show Interstitial Ad
  showInterstitialAd(onAdLoaded, onAdClosed) {
    this.interstitialAd.load();

    const unsubscribeLoaded = this.interstitialAd.addAdEventListener(
      AdEventType.LOADED,
      () => {
        onAdLoaded?.();
        this.interstitialAd.show();
        unsubscribeLoaded();
      }
    );

    const unsubscribeClosed = this.interstitialAd.addAdEventListener(
      AdEventType.CLOSED,
      () => {
        onAdClosed?.();
        this.initAds(); // Reload ad for next use
        unsubscribeClosed();
      }
    );
  }

  // Show Rewarded Ad
  showRewardedAd(onRewardEarned, onAdClosed, onError) {
    this.rewardedAd.load();

    const unsubscribeLoaded = this.rewardedAd.addAdEventListener(
      RewardedAdEventType.LOADED,
      () => {
        this.rewardedAd.show();
        unsubscribeLoaded();
      }
    );

    const unsubscribeFailed = this.rewardedAd.addAdEventListener(
      AdEventType.ERROR,
      (error) => {
        onError?.(error);
        unsubscribeFailed();
        unsubscribeLoaded();
        unsubscribeEarned();
        unsubscribeClosed();
      }
    );

    const unsubscribeEarned = this.rewardedAd.addAdEventListener(
      RewardedAdEventType.EARNED_REWARD,
      (reward) => {
        onRewardEarned?.(reward);
        unsubscribeEarned();
      }
    );

    const unsubscribeClosed = this.rewardedAd.addAdEventListener(
      AdEventType.CLOSED,
      () => {
        onAdClosed?.();
        this.initAds(); // Reload ad for next use
        unsubscribeClosed();
      }
    );
  }
}

// Export as singleton instance
export default new AdmobService();