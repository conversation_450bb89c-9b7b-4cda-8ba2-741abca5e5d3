import React, { useState } from 'react';
import { View, Text } from 'react-native';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import { useTheme } from 'react-native-paper';
import AdmobService from '../services/AdmobService';

const StickyBottomAdMob = ({ subscriptionActive=false }) => {
  const { colors } = useTheme();
  const [adError, setAdError] = useState(null);

  if (subscriptionActive) return null;

  return (
    <>
      <View style={{
        position: 'relative',
        left: 0,
        right: 0,
        zIndex: 5,
        backgroundColor: colors.surface,
        alignItems: 'center'
      }}>
        <View style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 10,
        }}>
          {AdmobService.renderBannerAdContainer(BannerAdSize.BANNER, (error) => {
            console.log('Banner ad failed to load:', error);
            /* setAdError(error.message); */
          })}
        </View>
      </View>
      {/* {adError && __DEV__ && (
        <Text style={{
          color: colors.error,
          textAlign: 'center',
          padding: 8,
          backgroundColor: colors.surface
        }}>
          Ad failed to load: {adError}
        </Text>
      )} */}
    </>
  );
};

export default StickyBottomAdMob;