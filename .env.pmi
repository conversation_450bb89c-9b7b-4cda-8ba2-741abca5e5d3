# PMI Exam Configuration
APP_NAME=1800+ PMI Dump
APP_DISPLAY_NAME=1800+ PMI Dump
APP_ID=com.prepfy.pmi
EXAM_TYPE=PMI

WP_API_URL=https://hkit.supply/wp-json/base/v1/login_with_google
NODE_API_URL=https://rn-node.vercel.app/api
API_TOKEN=test-token
CACHE_TTL=1800000  # 30 minutes

# RevenueCat Configuration - PMI (You'll need to create separate PMI project)
REVENUECAT_API_KEY=goog_PMI_API_KEY_HERE
REVENUECAT_REST_API_KEY=sk_PMI_REST_API_KEY_HERE
REVENUECAT_PROJECT_ID=pmi_project_id_here

# AdMob Configuration - PMI (You'll need to create separate PMI app)
ADMOB_APP_ID=ca-app-pub-PMI-APP-ID-HERE

# Credit Configuration
CREDIT_VIA_AD=20
CREDIT_VIA_PURCHASE=100

# Real-time sync configuration
ENABLE_REALTIME_SYNC=false     # Disable RealTimeSyncService in production
USER_PROGRESS_SYNC_WINDOW=10  # Sync user progress every 10 seconds
AI_CHAT_SYNC_WINDOW=10        # Sync AI chat data every 10 seconds

# AI Credit Configuration
REVENUECAT_AI_CREDIT_WEEKLY=50
REVENUECAT_AI_CREDIT_MONTHLY=300
REVENUECAT_AI_CREDIT_BIMONTHLY=1000

IS_UNLOCKED=true

# Real-time sync configuration
ENABLE_REALTIME_SYNC=false     # Disable RealTimeSyncService in production
USER_PROGRESS_SYNC_WINDOW=10  # Sync user progress every 10 seconds
AI_CHAT_SYNC_WINDOW=10        # Sync AI chat data every 10 seconds

# AI Credit Configuration
REVENUECAT_AI_CREDIT_WEEKLY=50
REVENUECAT_AI_CREDIT_MONTHLY=300
REVENUECAT_AI_CREDIT_BIMONTHLY=1000

IS_UNLOCKED=true