#!/bin/bash

# <PERSON><PERSON>t to copy the appropriate configuration files based on the app variant
# This script should be added as a build phase in Xcode

echo "APP_VARIANT: ${APP_VARIANT}"
echo "CONFIGURATION: ${CONFIGURATION}"

# Determine the variant (default to aws if not set)
VARIANT=${APP_VARIANT:-aws}

echo "Building for variant: $VARIANT"

# Set source paths based on variant
if [ "$VARIANT" = "pmi" ]; then
    GOOGLE_SERVICE_SOURCE="${SRCROOT}/TestApp/Config/PMI/GoogleService-Info.plist"
    INFO_PLIST_SOURCE="${SRCROOT}/TestApp/Info-PMI.plist"
    echo "Using PMI configuration files"
else
    GOOGLE_SERVICE_SOURCE="${SRCROOT}/TestApp/Config/AWS/GoogleService-Info.plist"
    INFO_PLIST_SOURCE="${SRCROOT}/TestApp/Info-AWS.plist"
    echo "Using AWS configuration files"
fi

# Copy GoogleService-Info.plist
GOOGLE_SERVICE_DEST="${SRCROOT}/TestApp/GoogleService-Info.plist"
if [ -f "$GOOGLE_SERVICE_SOURCE" ]; then
    echo "Copying GoogleService-Info.plist from $GOOGLE_SERVICE_SOURCE to $GOOGLE_SERVICE_DEST"
    cp "$GOOGLE_SERVICE_SOURCE" "$GOOGLE_SERVICE_DEST"
else
    echo "Warning: GoogleService-Info.plist not found at $GOOGLE_SERVICE_SOURCE"
fi

# Copy Info.plist
INFO_PLIST_DEST="${SRCROOT}/TestApp/Info.plist"
if [ -f "$INFO_PLIST_SOURCE" ]; then
    echo "Copying Info.plist from $INFO_PLIST_SOURCE to $INFO_PLIST_DEST"
    cp "$INFO_PLIST_SOURCE" "$INFO_PLIST_DEST"
else
    echo "Warning: Info.plist not found at $INFO_PLIST_SOURCE"
fi

echo "Configuration files copied successfully"
