const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  resolver: {
    alias: {
      '@env': require('path').resolve(__dirname, getEnvFile()),
    },
  },
};

/**
 * Determine which environment file to use based on APP_ENV and APP_VARIANT
 */
function getEnvFile() {
  const appEnv = process.env.APP_ENV || 'release';
  const appVariant = process.env.APP_VARIANT || 'aws';

  if (appEnv === 'dev' && appVariant === 'pmi') {
    return '.env.pmi.dev';
  } else if (appEnv === 'dev') {
    return '.env.dev';
  } else if (appVariant === 'pmi') {
    return '.env.pmi';
  } else {
    return '.env';
  }
}

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
