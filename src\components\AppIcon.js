import React from 'react';
import { Image } from 'react-native';
import { APP_FLAVOR } from '@env';

// NOTE: For best practice, move these icons to an assets directory accessible to JS.
// If you must use the android/app/src path, ensure the images are bundled and accessible.
const appIcons = {
  aws: require('../../android/app/src/aws/res/mipmap-mdpi/ic_launcher_round.png'),
  pmi: require('../../android/app/src/pmi/res/mipmap-mdpi/ic_launcher_round.png'),
};

/**
 * AppIcon component
 * Displays the app icon based on the APP_FLAVOR environment variable.
 * 
 * @param {object} props - React props
 * @param {object} props.style - Optional style for the Image component
 */
const AppIcon = ({ style }) => {
  //TODO current App Icon is not yet loaded successfully...
  const iconSource = appIcons[APP_FLAVOR];
  console.log('APP_FLAVOR:', APP_FLAVOR)
  return (
    <Image
      source={iconSource}
      style={[style]}
    />
  );
};

export default AppIcon;