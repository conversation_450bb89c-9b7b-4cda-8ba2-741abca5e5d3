import { View, ScrollView, Pressable, StyleSheet, FlatList, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useState, useMemo, useEffect } from 'react';
import {
  Text,
  Button,
  ProgressBar,
  Card,
  useTheme,
  Appbar,
  IconButton,
} from 'react-native-paper';
import { useAppContext } from './store/AppContext';
import { useQuizResult } from './store/QuizResultContext';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import UpgradeButton from './components/UpgradeButton';
import BuyNowModal from './components/BuyNowModal';
import EmptyStateCard from './components/EmptyStateCard';
import { useQnAContext } from './store/QnAContext';
import { useExamContext } from './store/ExamContext';
import { useUserProgress } from './store/UserProgressContext';
import { usePurchase } from './store/PurchaseContext';
import SubjectCard from './components/SubjectCard';
import AdmobService from './services/AdmobService';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import { useLogin } from './store/LoginContext';
import StickyBottomAdMob from './components/StickyBottomAdMob';
import { useSubscriptionRefresh } from './utils/subscriptionUtils';
import AICreditBadge from './components/AICreditBadge';
import AICreditModal from './components/AICreditModal';
import FixedFontButton from './components/FixedFontButton';
import CustomAppbarContent from './components/CustomAppbarContent';
import AppIcon from './components/AppIcon';


const Quiz = () => {

  const { selectedExam, isLoading: examLoading } = useExamContext();
  const { questionsBySubject, isLoading: qnaLoading } = useQnAContext();
  const { subscriptionActive } = usePurchase();
  const [adError, setAdError] = useState(null);
  const [creditModalVisible, setCreditModalVisible] = useState(false);
  
  useSubscriptionRefresh();

  // Preload QnA data when exam is selected
  /* useEffect(async() => {
    if (selectedExam?.exam_code) {
      const examCode = selectedExam.exam_code;

      // Check if we already have questions for this exam
      const hasExistingData = currentExam === examCode &&
        Object.keys(questionsBySubject).length > 0 &&
        Object.values(questionsBySubject).some(subjectArray => subjectArray.length > 0);

      console.log('Exam Code:', examCode, 'Has Existing Data:', hasExistingData);

      if (!hasExistingData) {
        loadQnA(examCode);
        await storeSubscriptionStatus(examCode, subscriptionActive);
      }
    }
  }, [selectedExam?.exam_code, currentExam, questionsBySubject, loadQnA]); */

  const { colors } = useTheme();
  const { selectedOption } = useAppContext();
  const navigation = useNavigation();
  const { results } = useQuizResult();
  const [visibleGroups, setVisibleGroups] = useState(3);
  const [visibleAttempts, setVisibleAttempts] = useState(3); // Track visible attempts instead of groups
  const [isBuyNowModalVisible, setIsBuyNowModalVisible] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const { progress } = useUserProgress();

  const subjects = useMemo(() => {
    const computedSubjects = selectedExam?.subjects?.map((subject, index) => {
      const subjectCode = subject.code ? Number(subject.code) : index + 1;

      // Get progress data from context
      /* const progressData = progress[subjectCode] || {};
      const correct = progressData.correct || 0;
      const incorrect = progressData.incorrect || 0; */

      const examProgress = progress[selectedExam?.id] || {};
      const subjectProgress = examProgress[subjectCode] || {};
      const correct = subjectProgress.correct?.length || 0;
      const incorrect = subjectProgress.incorrect?.length || 0;
      const total = questionsBySubject[subjectCode]?.length || 0;

      /* console.log('progress:', progress)
      console.log('examProgress:', examProgress)
      console.log('subjectProgress:', subjectProgress) */

      // Calculate progress metrics
      const attempted = correct + incorrect;
      const progressValue = total > 0 ? correct / total : 0;
      const percentage = total > 0 ? `${Math.round((correct / total) * 100)}%` : '0%';

      return {
        code: subjectCode,
        name: subject.name || subject,
        progress: progressValue,
        percentage,
        correct,
        attempted,
        total
      };
    });

    return computedSubjects;
  }, [selectedExam, questionsBySubject, progress]);

  const handleSubjectPress = (subject) => {
    navigation.navigate('QuizSetting', {
      selectedSubjects: [subject.code],
      initialQuestions: subject.total
    });
  };

  const renderResultGroup = ([date, results]) => (
    <View key={date} style={styles.resultGroup}>
      <Text variant="labelMedium" style={{
        color: colors.onSurfaceVariant,
        marginLeft: 16
      }} maxFontSizeMultiplier={1.5}>
        {date}
      </Text>
      {results.map(result => (
        <Pressable
          key={result.id}
          onPress={() => navigation.navigate('QuizResult', { resultId: result.id })}
        >
          <Card style={styles.resultCard}>
            <Card.Content>
              <View style={styles.resultContent}>
                <Text variant="bodyMedium" maxFontSizeMultiplier={1.5}>
                  {result.correctAnswers}/{result.totalQuestions} Correct
                </Text>
                <Text
                  style={[
                    styles.scoreText,
                    { color: result.score >= 70 ? colors.primary : colors.error }
                  ]} maxFontSizeMultiplier={1.5}
                >
                  {result.score.toFixed(1)}%
                </Text>
              </View>
            </Card.Content>
          </Card>
        </Pressable>
      ))}
    </View>
  );

  /* console.log('results', results) */

  // Filter results by selected exam
  const filteredResults = results.filter(
    result => result.examCode === selectedExam?.exam_code
  );

  /* console.log('filteredResults', filteredResults) */

  // Modify sorting and slicing to use filtered results
  const sortedResults = [...filteredResults].sort((a, b) =>
    new Date(b.date) - new Date(a.date)
  );
  const slicedResults = sortedResults.slice(0, visibleAttempts);

  // Group the sliced results by date
  const groupedResults = Object.entries(
    slicedResults.reduce((acc, result) => {
      const date = new Date(result.date).toLocaleDateString();
      if (!acc[date]) acc[date] = [];
      acc[date].push(result);
      return acc;
    }, {})
  );

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Appbar.Header elevated>
        {/* App icon on the left */}
        <View style={{ marginLeft: 10, marginRight: 4 }}>
          <AppIcon style={{ width: 28, height: 28 }} />
        </View>

        {/* Show exam_code instead of exam_name */}
        <CustomAppbarContent
          title={selectedExam?.exam_code || ""}
          titleStyle={{
            paddingHorizontal: 12,
          }}
        />

        <FixedFontButton
          mode="text"
          onPress={() => navigation.navigate('Welcome2')}
          labelStyle={{ color: colors.primary, fontWeight: '600', fontSize: 14 }}
          compact
          style={{ marginRight: 8 }}
        >
          Browse Exams
        </FixedFontButton>
        <AICreditBadge onPress={() => setCreditModalVisible(true)} />
      </Appbar.Header>

      {!selectedExam ? (
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <EmptyStateCard
            onAction={() => navigation.navigate('Welcome2')}
            subtitle="Please select an exam to view study materials"
          />
        </ScrollView>
      ) : (
        <>
          <ScrollView contentContainerStyle={[styles.container, { paddingBottom: 32 }]}>
            <Text variant="titleMedium" style={[styles.sectionTitle, { color: colors.onSurface }]}>
              Progress Overview
            </Text>


            {subjects.map((subject) => (
              <SubjectCard
                key={subject.code}
                subject={subject}
                metricTitle="Correct"
                metricValue={subject.correct}
                onPress={() => handleSubjectPress(subject)}
              />
            ))}

            <View style={styles.buttonGroup}>
              <FixedFontButton
                mode="contained"
                icon="play"
                style={styles.primaryButton}
                labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
                theme={{ colors: { primary: colors.primary } }}
                onPress={() => navigation.navigate('QuizSetting', {
                  selectedSubjects: subjects.map(s => s.code)
                })}
              >
                Start New Quiz
              </FixedFontButton>
              <BuyNowModal
                buyNowModalVisible={isBuyNowModalVisible}
                setBuyNowModalVisible={setIsBuyNowModalVisible}
                selectedProduct={selectedExam}
                selectedPlan={selectedPlan}
                setSelectedPlan={setSelectedPlan}
                /* onPurchaseComplete={async (result) => {
                  console.log('[Quiz] Purchase completed, refreshing subscription status');
                  await refreshSubscription();
                }} */
              />
            </View>
            {/* {!subscriptionActive && (
              AdmobService.renderBannerAdContainer(
                BannerAdSize.BANNER,
                (error) => console.log('Banner ad failed to load:', error)
              )
            )} */}

            {groupedResults.length > 0 && (
              <>
                <Text variant="titleMedium" style={styles.sectionTitle}  maxFontSizeMultiplier={1.5}>
                  Recent Attempts
                </Text>
                {groupedResults.map(renderResultGroup)}
                {sortedResults.length > visibleAttempts && (
                  <FixedFontButton
                    mode="outlined"
                    onPress={() => setVisibleAttempts(prev => prev + 3)}
                    style={styles.loadMoreButton}
                    labelStyle={{ color: colors.primary, fontWeight: '600' }}
                  >
                    Show More Attempts
                  </FixedFontButton>
                )}
              </>
            )}

            {!subscriptionActive && (
              <UpgradeButton
                onPress={() => setIsBuyNowModalVisible(true)}
              />
            )}
          </ScrollView>
        </>
      )}
      <StickyBottomAdMob subscriptionActive={subscriptionActive} />
      <AICreditModal 
        visible={creditModalVisible}
        onDismiss={() => setCreditModalVisible(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 16,
  },
  alertCard: {
    marginTop: 24,
    marginHorizontal: 16,
    alignItems: 'center',
    paddingVertical: 24,
    borderRadius: 12,
  },
  alertText: {
    marginTop: 8,
    textAlign: 'center',
    fontSize: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 5,
    marginLeft: 8,
  },
  cardPressable: {
    marginBottom: 12,
  },
  subjectCard: {
    borderRadius: 12,
    padding: 8,
    position: 'relative',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  bookIcon: {
    marginRight: 12,
  },
  subjectTitle: {
    flex: 1,
    fontWeight: '600',
    lineHeight: 22,
  },
  progressContainer: {
    marginLeft: 32,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontWeight: '700',
    fontSize: 15,
  },
  metaText: {
    fontSize: 13,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  chevron: {
    position: 'absolute',
    right: 16,
    top: '50%',
    marginTop: -12,
  },
  buttonGroup: {
    marginTop: 6,
  },
  primaryButton: {
    marginBottom: 6,
    borderRadius: 8,
    paddingVertical: 6,
    width: '100%',
  },
  secondaryButton: {
    borderRadius: 8,
    borderWidth: 1,
    paddingVertical: 6,
  },
  resultGroup: {
    marginBottom: 16,
  },
  resultCard: {
    marginTop: 8,
  },
  resultContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  scoreText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  loadMoreButton: {
    marginTop: 16,
    borderRadius: 8,
    width: '100%',
  },
});

export default Quiz;