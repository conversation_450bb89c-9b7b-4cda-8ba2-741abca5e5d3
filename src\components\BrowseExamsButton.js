import React from 'react';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from 'react-native-paper';
import FixedFontButton from './FixedFontButton';
import { IS_FOR_TESTER } from '@env';

const BrowseExamsButton = ({ style }) => {
  const navigation = useNavigation();
  const { colors } = useTheme();

  return (
    <FixedFontButton
      mode="text"
      onPress={() => navigation.navigate('Welcome2')}
      labelStyle={{ color: colors.primary, fontWeight: '600', fontSize: 14 }}
      compact
      style={[{ marginRight: 8 }, style]}
    >
      {IS_FOR_TESTER ? "..." : "Browse Exams"}
    </FixedFontButton>
  );
};

export default BrowseExamsButton;