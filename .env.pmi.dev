IS_UNLOCKED=true
IS_FOR_TESTER=false

# PMI Development Environment Configuration
APP_FLAVOR=pmi
APP_ENV=dev
APP_NAME=1800+ PMI Dump (Dev)
APP_ID=com.prepfy.pmi
EXAM_TYPE=PMI
VENDOR_CODE=pmi
DUMMY_VENDOR_CODE=dummy

WP_API_URL=https://hkit.supply/wp-json/base/v1/login_with_google
NODE_API_URL=http://localhost:3016/api
API_TOKEN=test-token
CACHE_TTL=1800000  # 30 minutes

# RevenueCat Configuration - PMI Development (You'll need to create separate PMI project)
REVENUECAT_API_KEY=goog_PMI_DEV_API_KEY_HERE
REVENUECAT_REST_API_KEY=sk_PMI_DEV_REST_API_KEY_HERE
REVENUECAT_PROJECT_ID=pmi_dev_project_id_here

# AdMob Configuration - PMI Development (Test App ID)
ADMOB_APP_ID=ca-app-pub-3940256099942544~3347511713

# Credit Configuration
CREDIT_VIA_AD=20
CREDIT_VIA_PURCHASE=100

# Real-time sync configuration
ENABLE_REALTIME_SYNC=false      # Enable for development testing
USER_PROGRESS_SYNC_WINDOW=5   # Faster sync for development
AI_CHAT_SYNC_WINDOW=5         # Faster sync for development

# AI Credit Configuration
REVENUECAT_AI_CREDIT_WEEKLY=50
REVENUECAT_AI_CREDIT_MONTHLY=300
REVENUECAT_AI_CREDIT_BIMONTHLY=1000

# AdMob Unit IDs for Android
ADMOB_ANDROID_BANNER_ID=ca-app-pub-3847831747989252/7422517766
ADMOB_ANDROID_INTERSTITIAL_ID=ca-app-pub-3847831747989252/3767463347
ADMOB_ANDROID_REWARDED_ID=ca-app-pub-3847831747989252/7128123859

# AdMob Unit IDs for iOS
ADMOB_IOS_BANNER_ID=ca-app-pub-xxxxxxxxxxxxx/yyyyyyyyyyyyyy
ADMOB_IOS_INTERSTITIAL_ID=ca-app-pub-xxxxxxxxxxxxx/yyyyyyyyyyyyyy
ADMOB_IOS_REWARDED_ID=ca-app-pub-xxxxxxxxxxxxx/yyyyyyyyyyyyyy