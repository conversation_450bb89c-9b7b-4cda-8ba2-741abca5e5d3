import {AppRegistry} from 'react-native';
import App from './App';
import appJson from './app.json';
import {ThemeProvider} from './src/components/ThemeContext';
import New from './New';

// Dynamically select appName based on environment (for multi-variant support)
const appName = process.env.APP_NAME || appJson.name;

export default function Main() {
  return (
    // <ThemeProvider>
    <New />
    // </ThemeProvider>
  );
}

AppRegistry.registerComponent(appName, () => Main);
