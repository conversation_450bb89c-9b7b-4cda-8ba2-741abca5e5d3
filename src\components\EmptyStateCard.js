import React from 'react';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons'; // Remove curly braces
import { useTheme } from 'react-native-paper';
import { View, StyleSheet } from 'react-native';
import { Card, Text, Button } from 'react-native-paper';
import FixedFontButton from '../components/FixedFontButton';
const EmptyStateCard = ({
  iconName = "book-open-outline",
  title = "No Exam Selected",
  subtitle = "Choose a study package to access learning materials",
  buttonText = "Browse Exams",
  onAction
}) => {
  const { colors } = useTheme();

  return (
    <Card style={[styles.card, { backgroundColor: colors.surfaceVariant }]}>
      <Card.Content style={styles.content}>
        <MaterialCommunityIcons
          name={iconName}
          size={32}
          color={colors.primary}
          style={styles.icon}
        />
        <Text
          variant="titleMedium"
          style={[styles.title, { color: colors.onSurfaceVariant }]}
        >
          {title}
        </Text>
        <Text
          variant="bodyMedium"
          style={[styles.subtitle, { color: colors.onSurfaceVariant }]}
        >
          {subtitle}
        </Text>
        <FixedFontButton
          mode="contained"
          onPress={onAction}
          style={[styles.button, { backgroundColor: colors.primary }]}
          labelStyle={{ fontSize: 12, color: '#FFFFFF' }}
          compact
          icon="bookmark-multiple-outline"
        >
          {buttonText}
        </FixedFontButton>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 24,
    borderRadius: 16,
    elevation: 2,
  },
  content: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 24,
  },
  icon: {
    marginBottom: 16,
  },
  title: {
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.8,
  },
  button: {
    borderRadius: 8,
    paddingHorizontal: 32,
  },
});

export default EmptyStateCard;