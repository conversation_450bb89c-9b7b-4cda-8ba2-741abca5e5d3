#!/usr/bin/env node

/**
 * Icon Generation Script for Monorepo
 * 
 * This script helps generate app icons and splash screens for both AWS and PMI variants.
 * Usage: node scripts/generate-icons.js [variant] [icon-path] [splash-path]
 * 
 * Examples:
 * node scripts/generate-icons.js aws "path/to/aws-icon.png" "path/to/aws-splash.png"
 * node scripts/generate-icons.js pmi "path/to/pmi-icon.png" "path/to/pmi-splash.png"
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

const variant = process.argv[2];
let iconPath = process.argv[3];
let splashPath = process.argv[4];

// Normalize paths for cross-platform compatibility
iconPath = path.normalize(iconPath);
splashPath = path.normalize(splashPath);

console.log(`Resolved icon path: ${iconPath}`);
console.log(`Resolved splash path: ${splashPath}`);

// Warn if paths contain spaces or non-ASCII characters
const hasNonAscii = str => /[^\x00-\x7F]/.test(str);
if (iconPath.includes(' ') || hasNonAscii(iconPath)) {
  console.warn('⚠️  Icon path contains spaces or non-ASCII characters. Ensure it is quoted and accessible.');
}
if (splashPath.includes(' ') || hasNonAscii(splashPath)) {
  console.warn('⚠️  Splash path contains spaces or non-ASCII characters. Ensure it is quoted and accessible.');
}

if (!variant || !['aws', 'pmi'].includes(variant)) {
  console.error('Usage: node scripts/generate-icons.js [aws|pmi] [icon-path] [splash-path]');
  console.error('Example: node scripts/generate-icons.js aws "./assets/aws-icon.png" "./assets/aws-splash.png"');
  process.exit(1);
}

if (!iconPath || !fs.existsSync(iconPath)) {
  console.error(`Icon file not found: ${iconPath}`);
  process.exit(1);
}

if (!splashPath || !fs.existsSync(splashPath)) {
  console.error(`Splash file not found: ${splashPath}`);
  process.exit(1);
}

console.log(`🚀 Generating icons for ${variant.toUpperCase()} variant...`);
console.log(`📱 Icon: ${iconPath}`);
console.log(`🎨 Splash: ${splashPath}`);
console.log('');
console.log('🔒 Safety features enabled:');
console.log('   • Isolated temporary directory generation');
console.log('   • Variant-specific icon placement');
console.log('   • No modification of other variants');
console.log('');

try {
  // Create a temporary directory for icon generation
  const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'icon-gen-'));
  console.log(`Using temporary directory: ${tempDir}`);

  // Create a temporary React Native project structure in the temp directory
  const tempProjectDir = path.join(tempDir, 'temp-project');
  const tempAndroidDir = path.join(tempProjectDir, 'android', 'app', 'src', 'main', 'res');
  const tempIosDir = path.join(tempProjectDir, 'ios');
  fs.mkdirSync(tempAndroidDir, { recursive: true });
  fs.mkdirSync(tempIosDir, { recursive: true });

  // Create a minimal package.json in temp directory for the tools to work
  const tempPackageJson = {
    name: "temp-icon-gen",
    version: "1.0.0",
    dependencies: {}
  };
  fs.writeFileSync(path.join(tempProjectDir, 'package.json'), JSON.stringify(tempPackageJson, null, 2));

  // Change to temp project directory for icon generation
  const originalCwd = process.cwd();
  process.chdir(tempProjectDir);

  try {
    // Generate app icons using rn-ml in the temporary project
    console.log('Generating app icons...');
    execSync(`npx rn-ml appicon -s "${path.resolve(originalCwd, iconPath)}"`, { stdio: 'inherit' });

    // Generate splash screen using react-native-bootsplash in the temporary project
    console.log('Generating splash screen...');
    execSync(`npx react-native-bootsplash generate "${path.resolve(originalCwd, splashPath)}" --background=131426 --logo-width=140`, { stdio: 'inherit' });

  } finally {
    // Always return to original directory
    process.chdir(originalCwd);
  }

  // Now copy the generated icons to the specific variant directory
  console.log(`📁 Moving Android icons to ${variant} directory...`);

  const androidVariantRes = path.join(process.cwd(), `android/app/src/${variant}/res`);
  
  // Verify the variant directory exists
  if (!fs.existsSync(path.join(process.cwd(), `android/app/src/${variant}`))) {
    throw new Error(`Variant directory not found: android/app/src/${variant}. Please ensure the ${variant} variant is properly configured.`);
  }

  // Ensure variant directories exist
  const mipmapDirs = ['mipmap-hdpi', 'mipmap-mdpi', 'mipmap-xhdpi', 'mipmap-xxhdpi', 'mipmap-xxxhdpi'];
  mipmapDirs.forEach(dir => {
    const variantDir = path.join(androidVariantRes, dir);
    if (!fs.existsSync(variantDir)) {
      fs.mkdirSync(variantDir, { recursive: true });
    }
  });

  // Copy icons from the temporary project to variant-specific directories
  let iconsCopied = 0;
  mipmapDirs.forEach(dir => {
    const sourceDir = path.join(tempAndroidDir, dir);
    const targetDir = path.join(androidVariantRes, dir);

    if (fs.existsSync(sourceDir)) {
      const files = fs.readdirSync(sourceDir);
      files.forEach(file => {
        if (file.startsWith('ic_launcher')) {
          const sourcePath = path.join(sourceDir, file);
          const targetPath = path.join(targetDir, file);
          fs.copyFileSync(sourcePath, targetPath);
          console.log(`Copied ${file} to ${variant} variant (${dir})`);
          iconsCopied++;
        }
      });
    }
  });

  // Also copy splash screen assets if they exist
  const drawableDirs = ['drawable', 'drawable-hdpi', 'drawable-mdpi', 'drawable-xhdpi', 'drawable-xxhdpi', 'drawable-xxxhdpi'];
  drawableDirs.forEach(dir => {
    const sourceDir = path.join(tempAndroidDir, dir);
    const targetDir = path.join(androidVariantRes, dir);

    if (fs.existsSync(sourceDir)) {
      // Ensure target directory exists
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      const files = fs.readdirSync(sourceDir);
      files.forEach(file => {
        if (file.includes('bootsplash')) {
          const sourcePath = path.join(sourceDir, file);
          const targetPath = path.join(targetDir, file);
          fs.copyFileSync(sourcePath, targetPath);
          console.log(`Copied splash asset ${file} to ${variant} variant (${dir})`);
        }
      });
    }
  });

  // Clean up temporary directory
  fs.rmSync(tempDir, { recursive: true, force: true });

  if (iconsCopied === 0) {
    throw new Error('No icons were generated or copied. Please check the icon generation tools.');
  }

  console.log(`✅ Icons generated successfully for ${variant.toUpperCase()} variant!`);
  console.log(`📊 Total icons copied: ${iconsCopied}`);
  console.log('');
  console.log('✅ Key benefits of this approach:');
  console.log(`   • Icons generated ONLY for ${variant.toUpperCase()} variant`);
  console.log('   • Other variant icons remain untouched');
  console.log('   • Main directory icons preserved');
  console.log('   • No cross-contamination between variants');
  console.log('');
  console.log('Next steps:');
  console.log(`1. For iOS: Update app icons in Xcode for the ${variant.toUpperCase()} scheme`);
  console.log(`2. Test the build: npm run android:${variant}`);
  console.log('3. Verify the correct icons appear in the built app');
  console.log('');
  console.log('💡 Tip: You can now safely generate icons for other variants without affecting this one!');

} catch (error) {
  console.error('Error generating icons:', error.message);
  process.exit(1);
}
