#!/usr/bin/env node

/**
 * Icon Generation Script for Monorepo
 * 
 * This script helps generate app icons and splash screens for both AWS and PMI variants.
 * Usage: node scripts/generate-icons.js [variant] [icon-path] [splash-path]
 * 
 * Examples:
 * node scripts/generate-icons.js aws "path/to/aws-icon.png" "path/to/aws-splash.png"
 * node scripts/generate-icons.js pmi "path/to/pmi-icon.png" "path/to/pmi-splash.png"
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const variant = process.argv[2];
const iconPath = process.argv[3];
const splashPath = process.argv[4];

if (!variant || !['aws', 'pmi'].includes(variant)) {
  console.error('Usage: node scripts/generate-icons.js [aws|pmi] [icon-path] [splash-path]');
  console.error('Example: node scripts/generate-icons.js aws "./assets/aws-icon.png" "./assets/aws-splash.png"');
  process.exit(1);
}

if (!iconPath || !fs.existsSync(iconPath)) {
  console.error(`Icon file not found: ${iconPath}`);
  process.exit(1);
}

if (!splashPath || !fs.existsSync(splashPath)) {
  console.error(`Splash file not found: ${splashPath}`);
  process.exit(1);
}

console.log(`Generating icons for ${variant.toUpperCase()} variant...`);
console.log(`Icon: ${iconPath}`);
console.log(`Splash: ${splashPath}`);

try {
  // Generate app icons using rn-ml
  console.log('Generating app icons...');
  execSync(`npx rn-ml appicon -s "${iconPath}"`, { stdio: 'inherit' });
  
  // Generate splash screen using react-native-bootsplash
  console.log('Generating splash screen...');
  execSync(`npx react-native-bootsplash generate "${splashPath}" --background=131426 --logo-width=140`, { stdio: 'inherit' });
  
  // Move generated icons to variant-specific directories for Android
  console.log(`Moving Android icons to ${variant} directory...`);
  
  const androidMainRes = 'android/app/src/main/res';
  const androidVariantRes = `android/app/src/${variant}/res`;
  
  // Ensure variant directories exist
  const mipmapDirs = ['mipmap-hdpi', 'mipmap-mdpi', 'mipmap-xhdpi', 'mipmap-xxhdpi', 'mipmap-xxxhdpi'];
  mipmapDirs.forEach(dir => {
    const variantDir = path.join(androidVariantRes, dir);
    if (!fs.existsSync(variantDir)) {
      fs.mkdirSync(variantDir, { recursive: true });
    }
  });
  
  // Copy icons to variant-specific directories
  mipmapDirs.forEach(dir => {
    const sourceDir = path.join(androidMainRes, dir);
    const targetDir = path.join(androidVariantRes, dir);
    
    if (fs.existsSync(sourceDir)) {
      const files = fs.readdirSync(sourceDir);
      files.forEach(file => {
        if (file.startsWith('ic_launcher')) {
          const sourcePath = path.join(sourceDir, file);
          const targetPath = path.join(targetDir, file);
          fs.copyFileSync(sourcePath, targetPath);
          console.log(`Copied ${file} to ${variant} variant`);
        }
      });
    }
  });
  
  console.log(`✅ Icons generated successfully for ${variant.toUpperCase()} variant!`);
  console.log('');
  console.log('Next steps:');
  console.log(`1. For iOS: Update app icons in Xcode for the ${variant.toUpperCase()} scheme`);
  console.log(`2. Test the build: npm run android:${variant}${variant === 'aws' ? '' : ''}`);
  console.log('3. Verify the correct icons appear in the built app');
  
} catch (error) {
  console.error('Error generating icons:', error.message);
  process.exit(1);
}
