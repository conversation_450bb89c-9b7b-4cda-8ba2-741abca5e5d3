<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>460398416745-re963a416qaent8f3lvs7459cvg7q5se.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.460398416745-re963a416qaent8f3lvs7459cvg7q5se</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>460398416745-ehju35opnaqdtcdhn9jp1l9n436hsrco.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDCDLLL8tBb5wLyIyIy6y2xKbI3S0MXoJk</string>
	<key>GCM_SENDER_ID</key>
	<string>460398416745</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.prepfy.qna</string>
	<key>PROJECT_ID</key>
	<string>edump-a7f32</string>
	<key>STORAGE_BUCKET</key>
	<string>edump-a7f32.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:460398416745:ios:8c8b3d7d7d34398527a3f8</string>
</dict>
</plist>